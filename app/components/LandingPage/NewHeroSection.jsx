'use client';

import { useState, useEffect, useMemo, useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import { motion, AnimatePresence } from 'framer-motion';

export default function HeroSection() {
    const router = useRouter();
    const searchParams = useSearchParams();

    const [currentSlide, setCurrentSlide] = useState(0);
    const [searchQuery, setSearchQuery] = useState('');
    const [isFocused, setIsFocused] = useState(false);

    // Auto-fill search query from URL params if user comes back
    useEffect(() => {
        const urlSearchQuery = searchParams.get('search');
        if (urlSearchQuery) {
            setSearchQuery(decodeURIComponent(urlSearchQuery));
        }
    }, [searchParams]);

    // Updated slides with new hero images
    const slides = useMemo(() => [
        {
            image: "/images/hero1.png",
            alt: "Wholesale distribution products"
        },
        {
            image: "/images/hero2.png",
            alt: "Pallet base products"
        },
        {
            image: "/images/hero3.jpeg",
            alt: "Colleagues oversee"
        }
    ], []);

    // Auto-slide functionality
    useEffect(() => {
        const interval = setInterval(() => {
            setCurrentSlide((prev) => (prev + 1) % slides.length);
        }, 5000);

        return () => clearInterval(interval);
    }, [slides.length]);

    // Optimized search handlers with useCallback
    const handleSearch = useCallback((e) => {
        e.preventDefault();
        const trimmedQuery = searchQuery.trim();
        if (!trimmedQuery) return;

        const sanitizedQuery = trimmedQuery.replace(/[<>]/g, '');
        const encodedQuery = encodeURIComponent(sanitizedQuery);
        router.push(`/products?search=${encodedQuery}`);
    }, [searchQuery, router]);

    const handlePopularSearch = useCallback((term) => {
        setSearchQuery(term);
        const encodedTerm = encodeURIComponent(term);
        router.push(`/products?search=${encodedTerm}`);
    }, [router]);



    return (
        <section className="relative min-h-[700px] sm:min-h-[800px] md:min-h-[600px] lg:min-h-[700px] xl:min-h-[750px] overflow-hidden bg-gradient-to-br from-orange-50 via-white to-yellow-50">

            {/* Mobile & Tablet Layout (sm and below) - Stacked */}
            <div className="md:hidden flex flex-col h-full min-h-[800px] sm:min-h-[900px] transition-all duration-300">
                {/* Image Section - Top - Made smaller and contained */}
                <div className="relative h-80 sm:h-96 w-full overflow-hidden">
                    {/* MOBILE SUBTLE PREMIUM BACKGROUND */}
                    <div className="absolute inset-0 bg-gradient-to-br from-orange-50/80 via-white to-yellow-50/60"></div>

                    {/* Mobile Controlled Crystal Background - Reduced bleeding */}
                    <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 opacity-60">
                        <div className="relative w-48 h-48 sm:w-56 sm:h-56">
                            {/* Reduced Crystal Layers */}
                            <div className="absolute inset-0 bg-gradient-to-br from-orange-500 via-orange-400 to-orange-600 opacity-15 blur-2xl animate-pulse rounded-full"></div>
                            <div className="absolute inset-4 bg-gradient-to-tr from-yellow-400 via-orange-300 to-red-500 opacity-12 blur-xl animate-pulse rounded-full" style={{ animationDelay: '1s' }}></div>

                            {/* Minimal Crystal Facets */}
                            <div className="absolute top-4 left-4 w-6 h-6 bg-gradient-to-br from-orange-400 to-orange-500 opacity-10 blur-md rotate-45 animate-pulse" style={{ animationDelay: '2s' }}></div>
                            <div className="absolute bottom-4 right-4 w-4 h-4 bg-gradient-to-br from-orange-300 to-orange-600 opacity-8 blur-sm rotate-75 animate-pulse" style={{ animationDelay: '2.5s' }}></div>
                        </div>
                    </div>

                    {/* Reduced Floating Particles */}
                    <div className="absolute inset-0 overflow-hidden pointer-events-none opacity-40">
                        {[...Array(4)].map((_, i) => (
                            <motion.div
                                key={i}
                                className="absolute w-1 h-1 bg-gradient-to-br from-orange-400 to-orange-500 opacity-20 blur-sm rounded-full"
                                style={{
                                    left: `${30 + i * 15}%`,
                                    top: `${35 + i * 10}%`,
                                }}
                                animate={{
                                    y: [-15, 15, -15],
                                    opacity: [0.1, 0.3, 0.1],
                                    scale: [0.8, 1, 0.8],
                                }}
                                transition={{
                                    duration: 5 + i,
                                    repeat: Infinity,
                                    ease: "easeInOut",
                                    delay: i * 0.5,
                                }}
                            />
                        ))}
                    </div>

                    {/* MOBILE OPTIMIZED ANGLED TILTED IMAGES - USING FULL SPACE */}
                    <div className="relative h-full flex items-center justify-center px-2 py-4 z-50">
                        <div className="relative w-full max-w-sm sm:max-w-md">
                            <div className="relative h-72 sm:h-80">
                                {slides.map((slide, index) => {
                                    const isActive = index === currentSlide;
                                    // Optimized mobile angles for better space usage
                                    const rotations = [-8, 4, -12];
                                    const xOffsets = [0, -12, 8];
                                    const yOffsets = [0, -8, -16];
                                    const scales = [1, 0.95, 0.90];

                                    const rotation = rotations[index] || 0;
                                    const xOffset = xOffsets[index] || 0;
                                    const yOffset = yOffsets[index] || 0;
                                    const scale = scales[index] || 0.88;

                                    return (
                                        <motion.div
                                            key={slide.image}
                                            className="absolute top-0 left-1/2 transform -translate-x-1/2"
                                            style={{
                                                zIndex: isActive ? 50 : 40 - index,
                                            }}
                                            animate={{
                                                scale: isActive ? 1.02 : scale,
                                                opacity: isActive ? 1 : 0.85,
                                                y: isActive ? -3 : yOffset,
                                                x: isActive ? 0 : xOffset,
                                                rotate: isActive ? -0.5 : rotation,
                                                filter: isActive ? 'brightness(1.08) contrast(1.08) saturate(1.15)' : 'brightness(0.98) contrast(1)',
                                            }}
                                            transition={{
                                                duration: 0.9,
                                                ease: [0.25, 0.46, 0.45, 0.94],
                                                type: "spring",
                                                stiffness: 100,
                                                damping: 20
                                            }}
                                            whileHover={{
                                                scale: isActive ? 1.04 : scale + 0.02,
                                                rotate: isActive ? 0.5 : rotation + 1,
                                                transition: { duration: 0.3 }
                                            }}
                                        >
                                            {/* Mobile Optimized Premium Frame - Larger for better space usage */}
                                            <div className="relative w-80 h-60 sm:w-88 sm:h-68">
                                                {/* Subtle Mobile Shadow */}
                                                <div className="absolute inset-0 bg-black/10 rounded-2xl blur-md transform translate-y-4 translate-x-2"></div>

                                                {/* Mobile Premium Frame */}
                                                <div className="relative w-full h-full bg-gradient-to-br from-white via-gray-50 to-gray-100 rounded-2xl overflow-hidden border-3 border-white shadow-lg">
                                                    {/* Subtle Inner Border */}
                                                    <div className="absolute inset-1 border border-orange-200/15 rounded-xl"></div>

                                                    {/* Mobile Photo Content - Optimized padding */}
                                                    <div className="relative w-full h-full p-3">
                                                        <div className="relative w-full h-full rounded-xl overflow-hidden bg-gradient-to-br from-gray-50 via-white to-gray-100">
                                                            <Image
                                                                src={slide.image}
                                                                alt={slide.alt}
                                                                fill
                                                                style={{ objectFit: "contain" }}
                                                                priority={index === 0}
                                                                quality={92}
                                                                className="rounded-xl"
                                                                sizes="(max-width: 768px) 90vw, 45vw"
                                                            />

                                                            {/* Subtle Glass Effect */}
                                                            <div className="absolute inset-0 bg-gradient-to-t from-transparent via-white/2 to-white/8 rounded-xl pointer-events-none"></div>
                                                            <div className="absolute top-3 left-3 w-6 h-6 bg-white/20 rounded-full blur-md pointer-events-none"></div>
                                                        </div>
                                                    </div>

                                                    {/* Subtle Frame Highlights */}
                                                    <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-white/40 to-transparent"></div>
                                                    <div className="absolute top-0 left-0 w-px h-full bg-gradient-to-b from-transparent via-white/40 to-transparent"></div>

                                                    {/* Mobile Active Glow - Reduced */}
                                                    {isActive && (
                                                        <motion.div
                                                            className="absolute -inset-0.5 bg-gradient-to-br from-orange-400/20 via-orange-500/10 to-orange-600/20 rounded-2xl blur-sm"
                                                            initial={{ opacity: 0, scale: 0.95 }}
                                                            animate={{ opacity: 1, scale: 1 }}
                                                            transition={{ duration: 0.5, ease: "easeOut" }}
                                                        />
                                                    )}
                                                </div>
                                            </div>
                                        </motion.div>
                                    );
                                })}
                            </div>
                        </div>
                    </div>

                    {/* MOBILE BILLION DOLLAR LUXURY NAVIGATION - MOVED UP */}
                    <motion.div
                        className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-60"
                        initial={{ opacity: 0, y: 40, scale: 0.8 }}
                        animate={{ opacity: 1, y: 0, scale: 1 }}
                        transition={{ delay: 1.8, duration: 0.9, ease: [0.25, 0.46, 0.45, 0.94] }}
                    >
                        {/* Mobile Ultra Premium Navigation Container */}
                        <div className="relative">
                            {/* Mobile Luxury Background Glow */}
                            <div className="absolute -inset-3 bg-gradient-to-r from-orange-400/15 via-orange-500/25 to-orange-400/15 rounded-full blur-lg"></div>
                            <div className="absolute -inset-1 bg-gradient-to-r from-white/30 via-white/50 to-white/30 rounded-full blur-md"></div>

                            {/* Mobile Premium Glass Container */}
                            <div className="relative flex items-center gap-3 bg-white/95 backdrop-blur-xl rounded-full px-4 py-2 shadow-xl border border-white/40">
                                {/* Mobile Luxury Inner Glow */}
                                <div className="absolute inset-0 bg-gradient-to-r from-orange-100/20 via-transparent to-orange-100/20 rounded-full"></div>

                                {slides.map((_, index) => (
                                    <motion.button
                                        key={index}
                                        onClick={() => setCurrentSlide(index)}
                                        className="relative group"
                                        whileHover={{ scale: 1.3 }}
                                        whileTap={{ scale: 0.8 }}
                                        initial={{
                                            opacity: 0,
                                            scale: 0,
                                            rotate: -120
                                        }}
                                        animate={{
                                            opacity: 1,
                                            scale: 1,
                                            rotate: 0
                                        }}
                                        transition={{
                                            duration: 0.7,
                                            delay: 1.8 + index * 0.15,
                                            type: "spring",
                                            stiffness: 140,
                                            damping: 12
                                        }}
                                        aria-label={`Go to slide ${index + 1}`}
                                    >
                                        {/* Mobile Luxury Button */}
                                        <div className={`relative w-3 h-3 rounded-full transition-all duration-600 ${
                                            index === currentSlide
                                                ? 'bg-gradient-to-br from-orange-400 via-orange-500 to-orange-600'
                                                : 'bg-gradient-to-br from-gray-300 to-gray-400 group-hover:from-orange-300 group-hover:to-orange-400'
                                        }`}>
                                            {/* Mobile Premium Active Indicator */}
                                            {index === currentSlide && (
                                                <>
                                                    <motion.div
                                                        className="absolute -inset-1.5 bg-gradient-to-br from-orange-400/35 to-orange-600/35 rounded-full blur-sm"
                                                        initial={{ scale: 0, opacity: 0 }}
                                                        animate={{ scale: 1.3, opacity: 1 }}
                                                        transition={{ duration: 0.5, ease: "easeOut" }}
                                                    />
                                                    <motion.div
                                                        className="absolute inset-0.5 bg-gradient-to-br from-white/70 to-white/30 rounded-full"
                                                        initial={{ scale: 0 }}
                                                        animate={{ scale: 1 }}
                                                        transition={{ duration: 0.3, delay: 0.1 }}
                                                    />
                                                    <motion.div
                                                        className="absolute inset-0.5 bg-gradient-to-br from-orange-300 to-orange-500 rounded-full"
                                                        initial={{ scale: 0 }}
                                                        animate={{ scale: 1 }}
                                                        transition={{ duration: 0.2, delay: 0.3 }}
                                                    />
                                                </>
                                            )}

                                            {/* Mobile Hover Glow */}
                                            <motion.div
                                                className="absolute -inset-0.5 bg-gradient-to-br from-orange-400/0 to-orange-600/0 rounded-full blur-sm group-hover:from-orange-400/50 group-hover:to-orange-600/50 transition-all duration-250"
                                            />
                                        </div>
                                    </motion.button>
                                ))}
                            </div>
                        </div>
                    </motion.div>
                </div>

                {/* View All Products Button - Mobile - VISIBLE AND PROMINENT */}
                <div className="relative w-full bg-white/50 backdrop-blur-sm py-6 border-t border-orange-100">
                    <motion.div
                        className="flex justify-center"
                        initial={{ opacity: 0, y: 40 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 1, delay: 2.2 }}
                    >
                        <Link href="/products">
                            <motion.button
                                className="px-8 sm:px-10 py-4 sm:py-5 text-white rounded-xl sm:rounded-2xl font-bold text-lg sm:text-xl shadow-xl bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 border-2 border-orange-300"
                                whileHover={{
                                    scale: 1.05,
                                    boxShadow: "0 20px 40px rgba(251, 146, 60, 0.5)"
                                }}
                                whileTap={{ scale: 0.95 }}
                            >
                                <span className="flex items-center gap-3">
                                    <span>View All Products</span>
                                    <svg
                                        className="w-5 h-5 sm:w-6 sm:h-6"
                                        fill="none"
                                        stroke="currentColor"
                                        viewBox="0 0 24 24"
                                    >
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                                    </svg>
                                </span>
                            </motion.button>
                        </Link>
                    </motion.div>
                </div>

                {/* Content Section - Bottom - Improved spacing */}
                <div className="flex-1 relative min-h-[520px] sm:min-h-[620px] flex flex-col items-center justify-center py-8 sm:py-12 px-4">

                    {/* Mobile Content Overlay */}
                    <div className="relative z-30 w-full">
                        <div className="container mx-auto px-6">
                            <motion.div
                                className="max-w-4xl mx-auto text-gray-900 text-center"
                                initial={{ opacity: 0, y: 40 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8, ease: "easeOut" }}
                            >
                                <motion.h1
                                    className="text-3xl sm:text-4xl font-black mb-6 sm:mb-8 leading-[0.9] tracking-tight"
                                    initial={{ opacity: 0, y: 30 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 1, delay: 0.3 }}
                                >
                                    <span className="bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 bg-clip-text text-transparent">
                                        Powering Distribution
                                    </span>
                                    <br />
                                    <span className="bg-gradient-to-r from-orange-600 via-orange-500 to-yellow-500 bg-clip-text text-transparent">
                                        for the Digital Age
                                    </span>
                                </motion.h1>

                                <motion.p
                                    className="text-base sm:text-lg mb-8 sm:mb-10 font-light text-gray-700 max-w-2xl mx-auto leading-relaxed"
                                    initial={{ opacity: 0, y: 30 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 1, delay: 0.8 }}
                                >
                                    Explore, compare, and buy{" "}
                                    <span className="text-orange-600">directly from verified distributors.</span>{" "}
                                    <span className="bg-gradient-to-r from-orange-600 to-yellow-600 bg-clip-text text-transparent font-semibold">
                                        Join the revolution in digital B2B commerce.
                                    </span>
                                </motion.p>

                                {/* Search Field */}
                                <motion.div
                                    className="relative mb-10 sm:mb-12 w-full max-w-2xl mx-auto"
                                    initial={{ opacity: 0, y: 40 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 1, delay: 1.2 }}
                                >
                                    <form onSubmit={handleSearch} className="relative">

                                        <div className={`relative flex items-center bg-white rounded-3xl shadow-lg border-2 transition-all duration-300 ${isFocused ? 'border-orange-400' : 'border-gray-200'}`}>
                                            <div className="flex items-center w-full">
                                                <div className="flex-shrink-0 pl-4 sm:pl-6">
                                                    <motion.div
                                                        animate={{
                                                            scale: isFocused ? 1.1 : 1,
                                                            rotate: isFocused ? 360 : 0
                                                        }}
                                                        transition={{ duration: 0.3 }}
                                                    >
                                                        <svg className="w-5 h-5 sm:w-6 sm:h-6 text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                                        </svg>
                                                    </motion.div>
                                                </div>

                                                <input
                                                    type="text"
                                                    placeholder="Search for products, categories, brands..."
                                                    className="flex-1 py-3 sm:py-4 px-3 sm:px-4 text-gray-800 text-base sm:text-lg font-medium outline-none bg-transparent touch-manipulation placeholder:text-gray-400 placeholder:font-normal"
                                                    value={searchQuery}
                                                    onChange={(e) => setSearchQuery(e.target.value)}
                                                    onFocus={() => setIsFocused(true)}
                                                    onBlur={() => setIsFocused(false)}
                                                    autoComplete="off"
                                                    maxLength={100}
                                                    aria-label="Search for products, categories, brands"
                                                />
                                            </div>

                                            <motion.button
                                                type="submit"
                                                onClick={handleSearch}
                                                className="bg-gradient-to-r from-orange-400 via-orange-500 to-yellow-400 hover:from-yellow-400 hover:via-orange-400 hover:to-orange-500 text-white p-3 sm:p-4 m-1.5 sm:m-2 rounded-xl sm:rounded-2xl transition-all duration-500 flex items-center justify-center w-12 h-12 sm:w-14 sm:h-14 relative overflow-hidden group cursor-pointer shadow-lg hover:shadow-xl"
                                                whileHover={{
                                                    scale: 1.05,
                                                    rotate: [0, -5, 5, 0],
                                                    boxShadow: "0 15px 30px -8px rgba(251, 146, 60, 0.4)"
                                                }}
                                                whileTap={{ scale: 0.95 }}
                                                transition={{ duration: 0.3 }}
                                                aria-label="Search"
                                            >
                                                {/* Simplified search icon */}
                                                <svg
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    width="20"
                                                    height="20"
                                                    fill="none"
                                                    viewBox="0 0 24 24"
                                                    stroke="currentColor"
                                                    className="min-w-5 h-5 sm:w-6 sm:h-6"
                                                    strokeWidth={2.5}
                                                >
                                                    <path strokeLinecap="round" strokeLinejoin="round" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                                </svg>
                                            </motion.button>
                                        </div>
                                        <motion.div
                                            className="absolute -bottom-16 sm:-bottom-18 w-full text-center"
                                            initial={{ opacity: 0, y: 20 }}
                                            animate={{ opacity: 1, y: 0 }}
                                            transition={{ delay: 1.8, duration: 0.8 }}
                                        >
                                            <motion.span
                                                className="text-xs sm:text-sm text-gray-600 font-medium"
                                                whileHover={{ color: '#f59e0b' }}
                                            >
                                                Popular searches:
                                            </motion.span>
                                            <div className="flex flex-wrap justify-center gap-1.5 sm:gap-2 mt-1.5 sm:mt-2">
                                                {['Drinks', 'Dishwashing Tablets', 'Liquid Detergent'].map((term) => (
                                                    <button
                                                        key={term}
                                                        type="button"
                                                        onClick={() => handlePopularSearch(term)}
                                                        className="px-2 py-1 sm:px-3 sm:py-1 bg-orange-100 border border-orange-200 rounded-full text-xs text-gray-700 hover:bg-orange-200 transition-colors duration-200"
                                                        aria-label={`Search for ${term}`}
                                                    >
                                                        {term}
                                                    </button>
                                                ))}
                                            </div>
                                        </motion.div>
                                    </form>
                                </motion.div>

                                {/* CTA button */}
                                <motion.div
                                    className="flex justify-center mb-8 sm:mb-12 mt-20"
                                    initial={{ opacity: 0, y: 40 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 1, delay: 2.2 }}
                                >
                                    <Link href="/cart">
                                        <motion.button
                                            className="px-6 sm:px-8 py-3 sm:py-4 text-white rounded-xl sm:rounded-2xl font-bold text-base sm:text-lg shadow-lg bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500"
                                            whileHover={{ scale: 1.05 }}
                                            whileTap={{ scale: 0.95 }}
                                        >
                                            <span className="flex items-center gap-2 sm:gap-3">
                                                <span>Your Pallet</span>
                                                <svg
                                                    className="w-4 h-4 sm:w-5 sm:h-5"
                                                    fill="none"
                                                    stroke="currentColor"
                                                    viewBox="0 0 24 24"
                                                >
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                                                </svg>
                                            </span>
                                        </motion.button>
                                    </Link>
                                </motion.div>


                            </motion.div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Desktop Layout (md and above) - Side by Side - Improved proportions */}
            <div className="hidden md:grid md:grid-cols-5 h-full min-h-[600px] lg:min-h-[700px] xl:min-h-[750px] transition-all duration-300">
                {/* Content Section - Left - Takes 3 columns */}
                <div className="relative col-span-3 flex items-center px-8 lg:px-12 xl:px-16">
                    {/* Desktop Content Overlay */}
                    <div className="relative z-30 w-full">
                        <div className="w-full">
                            <motion.div
                                className="max-w-4xl text-gray-900 text-left"
                                initial={{ opacity: 0, y: 40 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8, ease: "easeOut" }}
                            >
                                <motion.h1
                                    className="text-4xl lg:text-5xl xl:text-6xl font-black mb-6 lg:mb-8 leading-[0.9] tracking-tight"
                                    initial={{ opacity: 0, y: 30 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 1, delay: 0.3 }}
                                >
                                    <span className="bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 bg-clip-text text-transparent">
                                        Powering Distribution
                                    </span>
                                    <br />
                                    <span className="bg-gradient-to-r from-orange-600 via-orange-500 to-yellow-500 bg-clip-text text-transparent">
                                        for the Digital Age
                                    </span>
                                </motion.h1>

                                <motion.p
                                    className="text-lg lg:text-xl mb-8 lg:mb-10 font-light text-gray-700 max-w-2xl leading-relaxed"
                                    initial={{ opacity: 0, y: 30 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 1, delay: 0.8 }}
                                >
                                    Explore, compare, and buy{" "}
                                    <span className="text-orange-600">directly from verified distributors.</span>{" "}
                                    <span className="bg-gradient-to-r from-orange-600 to-yellow-600 bg-clip-text text-transparent font-semibold">
                                        Join the revolution in digital B2B commerce.
                                    </span>
                                </motion.p>

                                {/* Search Field */}
                                <motion.div
                                    className="relative mb-8 lg:mb-12 w-full max-w-2xl"
                                    initial={{ opacity: 0, y: 40 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 1, delay: 1.2 }}
                                >
                                    <form onSubmit={handleSearch} className="relative">

                                        <div className={`relative flex items-center bg-white rounded-3xl shadow-lg border-2 transition-all duration-300 ${isFocused ? 'border-orange-400' : 'border-gray-200'}`}>
                                            <div className="flex items-center w-full">
                                                <div className="flex-shrink-0 pl-8">
                                                    <motion.div
                                                        animate={{
                                                            scale: isFocused ? 1.1 : 1,
                                                            rotate: isFocused ? 360 : 0
                                                        }}
                                                        transition={{ duration: 0.3 }}
                                                    >
                                                        <svg className="w-6 h-6 text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                                        </svg>
                                                    </motion.div>
                                                </div>

                                                <input
                                                    type="text"
                                                    placeholder="Search for products, categories, brands..."
                                                    className="flex-1 py-5 px-6 text-gray-800 text-xl font-medium outline-none bg-transparent touch-manipulation placeholder:text-gray-400 placeholder:font-normal"
                                                    value={searchQuery}
                                                    onChange={(e) => setSearchQuery(e.target.value)}
                                                    onFocus={() => setIsFocused(true)}
                                                    onBlur={() => setIsFocused(false)}
                                                    autoComplete="off"
                                                    maxLength={100}
                                                    aria-label="Search for products, categories, brands"
                                                />
                                            </div>

                                            <motion.button
                                                type="submit"
                                                onClick={handleSearch}
                                                className="bg-gradient-to-r from-orange-400 via-orange-500 to-yellow-400 hover:from-yellow-400 hover:via-orange-400 hover:to-orange-500 text-white p-4 m-3 rounded-2xl transition-all duration-500 flex items-center justify-center w-16 h-16 relative overflow-hidden group cursor-pointer shadow-lg hover:shadow-xl"
                                                whileHover={{
                                                    scale: 1.05,
                                                    rotate: [0, -5, 5, 0],
                                                    boxShadow: "0 15px 30px -8px rgba(251, 146, 60, 0.4)"
                                                }}
                                                whileTap={{ scale: 0.95 }}
                                                transition={{ duration: 0.3 }}
                                                aria-label="Search"
                                            >
                                                {/* Simplified search icon */}
                                                <svg
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    width="20"
                                                    height="20"
                                                    fill="none"
                                                    viewBox="0 0 24 24"
                                                    stroke="currentColor"
                                                    className="w-6 h-6"
                                                    strokeWidth={2.5}
                                                >
                                                    <path strokeLinecap="round" strokeLinejoin="round" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                                </svg>
                                            </motion.button>
                                        </div>
                                        <motion.div
                                            className="p-5 w-full text-left"
                                            initial={{ opacity: 0, y: 20 }}
                                            animate={{ opacity: 1, y: 0 }}
                                            transition={{ delay: 1.8, duration: 0.8 }}
                                        >
                                            <motion.span
                                                className="text-base text-gray-600 font-medium"
                                                whileHover={{ color: '#f59e0b' }}
                                            >
                                                Popular searches:
                                            </motion.span>
                                            <div className="flex flex-row flex-wrap gap-3 mt-2 items-center">
                                                {['Drinks', 'Dishwashing Tablets', 'Liquid Detergent'].map((term) => (
                                                    <button
                                                        key={term}
                                                        type="button"
                                                        onClick={() => handlePopularSearch(term)}
                                                        className="px-4 py-2 bg-orange-100 border border-orange-200 rounded-full text-sm text-gray-700 hover:bg-orange-200 transition-colors duration-200 whitespace-nowrap"
                                                        aria-label={`Search for ${term}`}
                                                    >
                                                        {term}
                                                    </button>
                                                ))}
                                            </div>
                                        </motion.div>
                                    </form>
                                </motion.div>

                                {/* CTA button */}
                                <motion.div
                                    className="flex justify-start"
                                    initial={{ opacity: 0, y: 40 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 1, delay: 2.2 }}
                                >
                                    <Link href="/cart">
                                        <motion.button
                                            className="px-12 py-4 text-white rounded-2xl font-bold text-xl shadow-lg bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500"
                                            whileHover={{ scale: 1.05 }}
                                            whileTap={{ scale: 0.95 }}
                                        >
                                            <span className="flex items-center gap-3">
                                                <span>Your Pallet</span>
                                                <svg
                                                    className="w-6 h-6"
                                                    fill="none"
                                                    stroke="currentColor"
                                                    viewBox="0 0 24 24"
                                                >
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                                                </svg>
                                            </span>
                                        </motion.button>
                                    </Link>
                                </motion.div>
                            </motion.div>
                        </div>
                    </div>
                </div>

                {/* Image Section - Right - Premium Stacked Photo Design */}
                <div className="relative col-span-2 flex flex-col items-center justify-start pt-4 lg:pt-8 p-6 lg:p-8 xl:p-12 overflow-hidden">
                    {/* Glowing Orange Crystal Background */}
                    <div className="absolute inset-0 flex items-center justify-center">
                        <div className="relative">
                            {/* Main Crystal */}
                            <div className="w-40 h-40 lg:w-48 lg:h-48 xl:w-56 xl:h-56 bg-gradient-to-br from-orange-400 via-orange-500 to-orange-600 rounded-full opacity-25 blur-3xl animate-pulse"></div>
                            {/* Secondary Crystals */}
                            <div className="absolute -top-12 -right-12 w-24 h-24 lg:w-28 lg:h-28 bg-gradient-to-br from-yellow-400 via-orange-400 to-red-500 rounded-full opacity-20 blur-2xl animate-pulse" style={{ animationDelay: '1s' }}></div>
                            <div className="absolute -bottom-8 -left-8 w-20 h-20 lg:w-24 lg:h-24 bg-gradient-to-br from-orange-300 via-orange-400 to-orange-500 rounded-full opacity-15 blur-xl animate-pulse" style={{ animationDelay: '2s' }}></div>
                            {/* Additional ambient glow */}
                            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 lg:w-80 lg:h-80 bg-gradient-radial from-orange-200/10 via-orange-300/5 to-transparent rounded-full"></div>
                        </div>
                    </div>

                    {/* Floating Particles */}
                    <div className="absolute inset-0 overflow-hidden pointer-events-none">
                        {[...Array(6)].map((_, i) => (
                            <motion.div
                                key={i}
                                className="absolute w-2 h-2 bg-orange-400/30 rounded-full"
                                style={{
                                    left: `${20 + i * 15}%`,
                                    top: `${30 + i * 10}%`,
                                }}
                                animate={{
                                    y: [-20, 20, -20],
                                    opacity: [0.3, 0.7, 0.3],
                                    scale: [0.8, 1.2, 0.8],
                                }}
                                transition={{
                                    duration: 4 + i,
                                    repeat: Infinity,
                                    ease: "easeInOut",
                                    delay: i * 0.5,
                                }}
                            />
                        ))}
                    </div>

                    {/* Stacked Photos Container */}
                    <div className="relative w-full max-w-sm lg:max-w-md xl:max-w-lg mb-8 lg:mb-12">
                        {/* Photo Stack */}
                        <div className="relative h-64 lg:h-80 xl:h-[320px]">
                            {slides.map((slide, index) => {
                                const isActive = index === currentSlide;
                                const stackOffset = index * 8;
                                const rotation = index === 0 ? -3 : index === 1 ? 2 : -1;

                                return (
                                    <motion.div
                                        key={slide.image}
                                        className="absolute inset-0"
                                        style={{
                                            zIndex: isActive ? 30 : 20 - index,
                                            transform: `translateX(${stackOffset}px) translateY(${stackOffset}px) rotate(${rotation}deg)`,
                                        }}
                                        animate={{
                                            scale: isActive ? 1 : 0.95,
                                            opacity: isActive ? 1 : 0.7,
                                            y: isActive ? 0 : stackOffset,
                                            x: isActive ? 0 : stackOffset,
                                            rotate: isActive ? 0 : rotation,
                                        }}
                                        transition={{
                                            duration: 0.8,
                                            ease: "easeInOut",
                                            type: "spring",
                                            stiffness: 100
                                        }}
                                        whileHover={{
                                            scale: isActive ? 1.02 : 0.97,
                                            rotate: isActive ? 1 : rotation + 1,
                                        }}
                                    >
                                        {/* Photo Frame */}
                                        <div className="relative w-80 h-64 lg:w-96 lg:h-80 xl:w-[420px] xl:h-[320px] bg-white rounded-2xl shadow-2xl overflow-hidden border-4 border-white">
                                            {/* Photo Content */}
                                            <div className="relative w-full h-full p-3">
                                                <div className="relative w-full h-full rounded-xl overflow-hidden bg-gradient-to-br from-gray-50 to-gray-100">
                                                    <Image
                                                        src={slide.image}
                                                        alt={slide.alt}
                                                        fill
                                                        style={{ objectFit: "contain" }}
                                                        priority={index === 0}
                                                        quality={95}
                                                        className="rounded-xl"
                                                        sizes="(max-width: 768px) 90vw, 40vw"
                                                    />
                                                </div>
                                            </div>

                                            {/* Photo Shadow */}
                                            <div className="absolute inset-0 rounded-2xl shadow-inner pointer-events-none"></div>

                                            {/* Subtle Gradient Overlay */}
                                            <div className="absolute inset-0 bg-gradient-to-t from-black/5 via-transparent to-white/10 rounded-2xl pointer-events-none"></div>
                                        </div>
                                    </motion.div>
                                );
                            })}
                        </div>
                    </div>

                    {/* Premium Navigation Dots */}
                    <motion.div
                        className="relative mt-6 lg:mt-8 flex justify-center"
                        initial={{ opacity: 0, y: 30 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 1.5, duration: 0.8 }}
                    >
                        <div className="flex items-center gap-3 bg-white/90 backdrop-blur-sm rounded-full px-4 py-2 shadow-lg border border-white/20">
                            {slides.map((_, index) => (
                                <motion.button
                                    key={index}
                                    onClick={() => setCurrentSlide(index)}
                                    className={`relative rounded-full transition-all duration-500 ${index === currentSlide
                                        ? 'bg-gradient-to-r from-orange-400 to-orange-500 shadow-lg shadow-orange-400/40'
                                        : 'bg-gray-300 hover:bg-gray-400'
                                        }`}
                                    whileHover={{
                                        scale: 1.3,
                                        boxShadow: index === currentSlide
                                            ? "0 8px 25px -8px rgba(251, 146, 60, 0.6)"
                                            : "0 4px 15px -4px rgba(0, 0, 0, 0.2)"
                                    }}
                                    whileTap={{ scale: 0.9 }}
                                    initial={{
                                        width: index === currentSlide ? 32 : 10,
                                        height: 10,
                                        opacity: 0,
                                        y: 20
                                    }}
                                    animate={{
                                        width: index === currentSlide ? 32 : 10,
                                        height: 10,
                                        opacity: 1,
                                        y: 0
                                    }}
                                    transition={{
                                        duration: 0.4,
                                        delay: 1.5 + index * 0.1,
                                        type: "spring",
                                        stiffness: 200
                                    }}
                                    aria-label={`Go to slide ${index + 1}`}
                                >
                                    {/* Active indicator glow */}
                                    {index === currentSlide && (
                                        <motion.div
                                            className="absolute inset-0 bg-gradient-to-r from-orange-300 to-orange-400 rounded-full blur-sm opacity-60"
                                            initial={{ scale: 0 }}
                                            animate={{ scale: 1.2 }}
                                            transition={{ duration: 0.3 }}
                                        />
                                    )}
                                </motion.button>
                            ))}
                        </div>
                    </motion.div>

                    {/* View All Products Button - Desktop - Below Navigation Dots */}
                    <motion.div
                        className="relative mt-6 lg:mt-8 flex justify-center"
                        initial={{ opacity: 0, y: 30 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 2.2, duration: 0.8 }}
                    >
                        <Link href="/products">
                            <motion.button
                                className="px-6 lg:px-8 py-3 lg:py-4 text-white rounded-xl lg:rounded-2xl font-bold text-base lg:text-lg shadow-xl bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 border-2 border-orange-300"
                                whileHover={{
                                    scale: 1.05,
                                    boxShadow: "0 20px 40px rgba(251, 146, 60, 0.5)"
                                }}
                                whileTap={{ scale: 0.95 }}
                            >
                                <span className="flex items-center gap-2 lg:gap-3">
                                    <span>View All Products</span>
                                    <svg
                                        className="w-4 h-4 lg:w-5 lg:h-5"
                                        fill="none"
                                        stroke="currentColor"
                                        viewBox="0 0 24 24"
                                    >
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                                    </svg>
                                </span>
                            </motion.button>
                        </Link>
                    </motion.div>
                </div>
            </div>
        </section>
    );
}
