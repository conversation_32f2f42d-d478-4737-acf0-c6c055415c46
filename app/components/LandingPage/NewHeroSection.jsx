'use client';

import { useState, useEffect, useMemo, useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import { motion, AnimatePresence } from 'framer-motion';

export default function HeroSection() {
    const router = useRouter();
    const searchParams = useSearchParams();

    const [currentSlide, setCurrentSlide] = useState(0);
    const [searchQuery, setSearchQuery] = useState('');
    const [isFocused, setIsFocused] = useState(false);

    // Auto-fill search query from URL params if user comes back
    useEffect(() => {
        const urlSearchQuery = searchParams.get('search');
        if (urlSearchQuery) {
            setSearchQuery(decodeURIComponent(urlSearchQuery));
        }
    }, [searchParams]);

    // Updated slides with new hero images
    const slides = useMemo(() => [
        {
            image: "/images/hero1.png",
            alt: "Wholesale distribution products"
        },
        {
            image: "/images/hero2.png",
            alt: "Pallet base products"
        },
        {
            image: "/images/hero3.jpeg",
            alt: "Colleagues oversee"
        }
    ], []);

    // Auto-slide functionality
    useEffect(() => {
        const interval = setInterval(() => {
            setCurrentSlide((prev) => (prev + 1) % slides.length);
        }, 5000);

        return () => clearInterval(interval);
    }, [slides.length]);

    // Optimized search handlers with useCallback
    const handleSearch = useCallback((e) => {
        e.preventDefault();
        const trimmedQuery = searchQuery.trim();
        if (!trimmedQuery) return;

        const sanitizedQuery = trimmedQuery.replace(/[<>]/g, '');
        const encodedQuery = encodeURIComponent(sanitizedQuery);
        router.push(`/products?search=${encodedQuery}`);
    }, [searchQuery, router]);

    const handlePopularSearch = useCallback((term) => {
        setSearchQuery(term);
        const encodedTerm = encodeURIComponent(term);
        router.push(`/products?search=${encodedTerm}`);
    }, [router]);



    return (
        <section className="relative min-h-[700px] sm:min-h-[800px] md:min-h-[600px] lg:min-h-[700px] xl:min-h-[750px] overflow-hidden bg-gradient-to-br from-orange-50 via-white to-yellow-50">

            {/* Mobile & Tablet Layout (sm and below) - Stacked */}
            <div className="md:hidden flex flex-col h-full min-h-[800px] sm:min-h-[900px] transition-all duration-300">
                {/* Image Section - Top - Made smaller and contained */}
                <div className="relative h-64 sm:h-72 w-full overflow-hidden bg-white rounded-b-3xl shadow-lg mx-4 mt-4">
                    <div className="p-4 h-full">

                        {/* Mobile Image Slider - Improved to show full images */}
                        <div className="relative w-full h-full rounded-2xl overflow-hidden bg-gradient-to-br from-gray-100 to-gray-200">
                            <AnimatePresence mode="wait">
                                {slides.map((slide, index) => (
                                    index === currentSlide && (
                                        <motion.div
                                            key={slide.image}
                                            initial={{ opacity: 0, scale: 1.02 }}
                                            animate={{ opacity: 1, scale: 1 }}
                                            exit={{ opacity: 0, scale: 1.02 }}
                                            transition={{ duration: 0.8, ease: "easeOut" }}
                                            className="absolute inset-0 flex items-center justify-center"
                                        >
                                            <div className="relative w-full h-full">
                                                <Image
                                                    src={slide.image}
                                                    alt={slide.alt}
                                                    fill
                                                    style={{ objectFit: "contain" }}
                                                    priority={index === 0}
                                                    quality={90}
                                                    className="rounded-2xl"
                                                    sizes="(max-width: 768px) 90vw, 45vw"
                                                />
                                            </div>
                                        </motion.div>
                                    )
                                ))}
                            </AnimatePresence>
                        </div>
                    </div>

                    {/* Enhanced slider navigation - Positioned outside image container */}
                    <motion.div
                        className="absolute -bottom-2 left-0 right-0 z-40"
                        initial={{ opacity: 0, y: 30 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 1.5, duration: 0.8 }}
                    >
                        <div className="flex justify-center items-center gap-2">
                            {slides.map((_, index) => (
                                <motion.button
                                    key={index}
                                    onClick={() => setCurrentSlide(index)}
                                    className={`relative rounded-full transition-all duration-500 ${index === currentSlide
                                        ? 'bg-orange-500 shadow-lg shadow-orange-400/30'
                                        : 'bg-gray-300 hover:bg-gray-400'
                                        }`}
                                    whileHover={{ scale: 1.2 }}
                                    whileTap={{ scale: 0.9 }}
                                    initial={{
                                        width: index === currentSlide ? 24 : 8,
                                        height: 8,
                                        opacity: 0,
                                        y: 20
                                    }}
                                    animate={{
                                        width: index === currentSlide ? 24 : 8,
                                        height: 8,
                                        opacity: 1,
                                        y: 0
                                    }}
                                    transition={{
                                        duration: 0.4,
                                        delay: 1.5 + index * 0.1,
                                        type: "spring",
                                        stiffness: 200
                                    }}
                                    aria-label={`Go to slide ${index + 1}`}
                                />
                            ))}
                        </div>
                    </motion.div>
                </div>

                {/* Content Section - Bottom - Improved spacing */}
                <div className="flex-1 relative min-h-[520px] sm:min-h-[620px] flex items-center justify-center py-8 sm:py-12 px-4">

                    {/* Mobile Content Overlay */}
                    <div className="relative z-30 w-full">
                        <div className="container mx-auto px-6">
                            <motion.div
                                className="max-w-4xl mx-auto text-gray-900 text-center"
                                initial={{ opacity: 0, y: 40 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8, ease: "easeOut" }}
                            >
                                <motion.h1
                                    className="text-3xl sm:text-4xl font-black mb-6 sm:mb-8 leading-[0.9] tracking-tight"
                                    initial={{ opacity: 0, y: 30 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 1, delay: 0.3 }}
                                >
                                    <span className="bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 bg-clip-text text-transparent">
                                        Powering Distribution
                                    </span>
                                    <br />
                                    <span className="bg-gradient-to-r from-orange-600 via-orange-500 to-yellow-500 bg-clip-text text-transparent">
                                        for the Digital Age
                                    </span>
                                </motion.h1>

                                <motion.p
                                    className="text-base sm:text-lg mb-8 sm:mb-10 font-light text-gray-700 max-w-2xl mx-auto leading-relaxed"
                                    initial={{ opacity: 0, y: 30 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 1, delay: 0.8 }}
                                >
                                    Explore, compare, and buy{" "}
                                    <span className="text-orange-600">directly from verified distributors.</span>{" "}
                                    <span className="bg-gradient-to-r from-orange-600 to-yellow-600 bg-clip-text text-transparent font-semibold">
                                        Join the revolution in digital B2B commerce.
                                    </span>
                                </motion.p>

                                {/* Search Field */}
                                <motion.div
                                    className="relative mb-10 sm:mb-12 w-full max-w-2xl mx-auto"
                                    initial={{ opacity: 0, y: 40 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 1, delay: 1.2 }}
                                >
                                    <form onSubmit={handleSearch} className="relative">

                                        <div className={`relative flex items-center bg-white rounded-3xl shadow-lg border-2 transition-all duration-300 ${isFocused ? 'border-orange-400' : 'border-gray-200'}`}>
                                            <div className="flex items-center w-full">
                                                <div className="flex-shrink-0 pl-4 sm:pl-6">
                                                    <motion.div
                                                        animate={{
                                                            scale: isFocused ? 1.1 : 1,
                                                            rotate: isFocused ? 360 : 0
                                                        }}
                                                        transition={{ duration: 0.3 }}
                                                    >
                                                        <svg className="w-5 h-5 sm:w-6 sm:h-6 text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                                        </svg>
                                                    </motion.div>
                                                </div>

                                                <input
                                                    type="text"
                                                    placeholder="Search for products, categories, brands..."
                                                    className="flex-1 py-3 sm:py-4 px-3 sm:px-4 text-gray-800 text-base sm:text-lg font-medium outline-none bg-transparent touch-manipulation placeholder:text-gray-400 placeholder:font-normal"
                                                    value={searchQuery}
                                                    onChange={(e) => setSearchQuery(e.target.value)}
                                                    onFocus={() => setIsFocused(true)}
                                                    onBlur={() => setIsFocused(false)}
                                                    autoComplete="off"
                                                    maxLength={100}
                                                    aria-label="Search for products, categories, brands"
                                                />
                                            </div>

                                            <motion.button
                                                type="submit"
                                                onClick={handleSearch}
                                                className="bg-gradient-to-r from-orange-400 via-orange-500 to-yellow-400 hover:from-yellow-400 hover:via-orange-400 hover:to-orange-500 text-white p-3 sm:p-4 m-1.5 sm:m-2 rounded-xl sm:rounded-2xl transition-all duration-500 flex items-center justify-center w-12 h-12 sm:w-14 sm:h-14 relative overflow-hidden group cursor-pointer shadow-lg hover:shadow-xl"
                                                whileHover={{
                                                    scale: 1.05,
                                                    rotate: [0, -5, 5, 0],
                                                    boxShadow: "0 15px 30px -8px rgba(251, 146, 60, 0.4)"
                                                }}
                                                whileTap={{ scale: 0.95 }}
                                                transition={{ duration: 0.3 }}
                                                aria-label="Search"
                                            >
                                                {/* Simplified search icon */}
                                                <svg
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    width="20"
                                                    height="20"
                                                    fill="none"
                                                    viewBox="0 0 24 24"
                                                    stroke="currentColor"
                                                    className="min-w-5 h-5 sm:w-6 sm:h-6"
                                                    strokeWidth={2.5}
                                                >
                                                    <path strokeLinecap="round" strokeLinejoin="round" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                                </svg>
                                            </motion.button>
                                        </div>
                                        <motion.div
                                            className="absolute -bottom-16 sm:-bottom-18 w-full text-center"
                                            initial={{ opacity: 0, y: 20 }}
                                            animate={{ opacity: 1, y: 0 }}
                                            transition={{ delay: 1.8, duration: 0.8 }}
                                        >
                                            <motion.span
                                                className="text-xs sm:text-sm text-gray-600 font-medium"
                                                whileHover={{ color: '#f59e0b' }}
                                            >
                                                Popular searches:
                                            </motion.span>
                                            <div className="flex flex-wrap justify-center gap-1.5 sm:gap-2 mt-1.5 sm:mt-2">
                                                {['Drinks', 'Dishwashing Tablets', 'Liquid Detergent'].map((term) => (
                                                    <button
                                                        key={term}
                                                        type="button"
                                                        onClick={() => handlePopularSearch(term)}
                                                        className="px-2 py-1 sm:px-3 sm:py-1 bg-orange-100 border border-orange-200 rounded-full text-xs text-gray-700 hover:bg-orange-200 transition-colors duration-200"
                                                        aria-label={`Search for ${term}`}
                                                    >
                                                        {term}
                                                    </button>
                                                ))}
                                            </div>
                                        </motion.div>
                                    </form>
                                </motion.div>

                                {/* CTA button */}
                                <motion.div
                                    className="flex justify-center mb-12 sm:mb-16 mt-20"
                                    initial={{ opacity: 0, y: 40 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 1, delay: 2.2 }}
                                >
                                    <Link href="/cart">
                                        <motion.button
                                            className="px-6 sm:px-8 py-3 sm:py-4 text-white rounded-xl sm:rounded-2xl font-bold text-base sm:text-lg shadow-lg bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500"
                                            whileHover={{ scale: 1.05 }}
                                            whileTap={{ scale: 0.95 }}
                                        >
                                            <span className="flex items-center gap-2 sm:gap-3">
                                                <span>Your Pallet</span>
                                                <svg
                                                    className="w-4 h-4 sm:w-5 sm:h-5"
                                                    fill="none"
                                                    stroke="currentColor"
                                                    viewBox="0 0 24 24"
                                                >
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                                                </svg>
                                            </span>
                                        </motion.button>
                                    </Link>
                                </motion.div>
                            </motion.div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Desktop Layout (md and above) - Side by Side - Improved proportions */}
            <div className="hidden md:grid md:grid-cols-5 h-full min-h-[600px] lg:min-h-[700px] xl:min-h-[750px] transition-all duration-300">
                {/* Content Section - Left - Takes 3 columns */}
                <div className="relative col-span-3 flex items-center px-8 lg:px-12 xl:px-16">
                    {/* Desktop Content Overlay */}
                    <div className="relative z-30 w-full">
                        <div className="w-full">
                            <motion.div
                                className="max-w-4xl text-gray-900 text-left"
                                initial={{ opacity: 0, y: 40 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8, ease: "easeOut" }}
                            >
                                <motion.h1
                                    className="text-4xl lg:text-5xl xl:text-6xl font-black mb-6 lg:mb-8 leading-[0.9] tracking-tight"
                                    initial={{ opacity: 0, y: 30 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 1, delay: 0.3 }}
                                >
                                    <span className="bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 bg-clip-text text-transparent">
                                        Powering Distribution
                                    </span>
                                    <br />
                                    <span className="bg-gradient-to-r from-orange-600 via-orange-500 to-yellow-500 bg-clip-text text-transparent">
                                        for the Digital Age
                                    </span>
                                </motion.h1>

                                <motion.p
                                    className="text-lg lg:text-xl mb-8 lg:mb-10 font-light text-gray-700 max-w-2xl leading-relaxed"
                                    initial={{ opacity: 0, y: 30 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 1, delay: 0.8 }}
                                >
                                    Explore, compare, and buy{" "}
                                    <span className="text-orange-600">directly from verified distributors.</span>{" "}
                                    <span className="bg-gradient-to-r from-orange-600 to-yellow-600 bg-clip-text text-transparent font-semibold">
                                        Join the revolution in digital B2B commerce.
                                    </span>
                                </motion.p>

                                {/* Search Field */}
                                <motion.div
                                    className="relative mb-8 lg:mb-12 w-full max-w-2xl"
                                    initial={{ opacity: 0, y: 40 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 1, delay: 1.2 }}
                                >
                                    <form onSubmit={handleSearch} className="relative">

                                        <div className={`relative flex items-center bg-white rounded-3xl shadow-lg border-2 transition-all duration-300 ${isFocused ? 'border-orange-400' : 'border-gray-200'}`}>
                                            <div className="flex items-center w-full">
                                                <div className="flex-shrink-0 pl-8">
                                                    <motion.div
                                                        animate={{
                                                            scale: isFocused ? 1.1 : 1,
                                                            rotate: isFocused ? 360 : 0
                                                        }}
                                                        transition={{ duration: 0.3 }}
                                                    >
                                                        <svg className="w-6 h-6 text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                                        </svg>
                                                    </motion.div>
                                                </div>

                                                <input
                                                    type="text"
                                                    placeholder="Search for products, categories, brands..."
                                                    className="flex-1 py-5 px-6 text-gray-800 text-xl font-medium outline-none bg-transparent touch-manipulation placeholder:text-gray-400 placeholder:font-normal"
                                                    value={searchQuery}
                                                    onChange={(e) => setSearchQuery(e.target.value)}
                                                    onFocus={() => setIsFocused(true)}
                                                    onBlur={() => setIsFocused(false)}
                                                    autoComplete="off"
                                                    maxLength={100}
                                                    aria-label="Search for products, categories, brands"
                                                />
                                            </div>

                                            <motion.button
                                                type="submit"
                                                onClick={handleSearch}
                                                className="bg-gradient-to-r from-orange-400 via-orange-500 to-yellow-400 hover:from-yellow-400 hover:via-orange-400 hover:to-orange-500 text-white p-4 m-3 rounded-2xl transition-all duration-500 flex items-center justify-center w-16 h-16 relative overflow-hidden group cursor-pointer shadow-lg hover:shadow-xl"
                                                whileHover={{
                                                    scale: 1.05,
                                                    rotate: [0, -5, 5, 0],
                                                    boxShadow: "0 15px 30px -8px rgba(251, 146, 60, 0.4)"
                                                }}
                                                whileTap={{ scale: 0.95 }}
                                                transition={{ duration: 0.3 }}
                                                aria-label="Search"
                                            >
                                                {/* Simplified search icon */}
                                                <svg
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    width="20"
                                                    height="20"
                                                    fill="none"
                                                    viewBox="0 0 24 24"
                                                    stroke="currentColor"
                                                    className="w-6 h-6"
                                                    strokeWidth={2.5}
                                                >
                                                    <path strokeLinecap="round" strokeLinejoin="round" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                                </svg>
                                            </motion.button>
                                        </div>
                                        <motion.div
                                            className="p-5 w-full text-left"
                                            initial={{ opacity: 0, y: 20 }}
                                            animate={{ opacity: 1, y: 0 }}
                                            transition={{ delay: 1.8, duration: 0.8 }}
                                        >
                                            <motion.span
                                                className="text-base text-gray-600 font-medium"
                                                whileHover={{ color: '#f59e0b' }}
                                            >
                                                Popular searches:
                                            </motion.span>
                                            <div className="flex flex-row flex-wrap gap-3 mt-2 items-center">
                                                {['Drinks', 'Dishwashing Tablets', 'Liquid Detergent'].map((term) => (
                                                    <button
                                                        key={term}
                                                        type="button"
                                                        onClick={() => handlePopularSearch(term)}
                                                        className="px-4 py-2 bg-orange-100 border border-orange-200 rounded-full text-sm text-gray-700 hover:bg-orange-200 transition-colors duration-200 whitespace-nowrap"
                                                        aria-label={`Search for ${term}`}
                                                    >
                                                        {term}
                                                    </button>
                                                ))}
                                            </div>
                                        </motion.div>
                                    </form>
                                </motion.div>

                                {/* CTA button */}
                                <motion.div
                                    className="flex justify-start"
                                    initial={{ opacity: 0, y: 40 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 1, delay: 2.2 }}
                                >
                                    <Link href="/cart">
                                        <motion.button
                                            className="px-12 py-4 text-white rounded-2xl font-bold text-xl shadow-lg bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500"
                                            whileHover={{ scale: 1.05 }}
                                            whileTap={{ scale: 0.95 }}
                                        >
                                            <span className="flex items-center gap-3">
                                                <span>Your Pallet</span>
                                                <svg
                                                    className="w-6 h-6"
                                                    fill="none"
                                                    stroke="currentColor"
                                                    viewBox="0 0 24 24"
                                                >
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                                                </svg>
                                            </span>
                                        </motion.button>
                                    </Link>
                                </motion.div>
                            </motion.div>
                        </div>
                    </div>
                </div>

                {/* Image Section - Right - Takes 2 columns, improved design */}
                <div className="relative col-span-2 flex items-center justify-center p-6 lg:p-8 xl:p-12">
                    {/* Desktop Image Container - Smaller and contained */}
                    <div className="relative w-full max-w-md lg:max-w-lg xl:max-w-xl h-80 lg:h-96 xl:h-[420px] bg-white rounded-3xl shadow-2xl overflow-hidden">
                        <div className="p-4 lg:p-6 h-full">
                            {/* Desktop Image Slider */}
                            <div className="relative w-full h-full rounded-2xl overflow-hidden bg-gradient-to-br from-gray-100 to-gray-200">
                                <AnimatePresence mode="wait">
                                    {slides.map((slide, index) => (
                                        index === currentSlide && (
                                            <motion.div
                                                key={slide.image}
                                                initial={{ opacity: 0, scale: 1.02 }}
                                                animate={{ opacity: 1, scale: 1 }}
                                                exit={{ opacity: 0, scale: 1.02 }}
                                                transition={{ duration: 0.8, ease: "easeOut" }}
                                                className="absolute inset-0 flex items-center justify-center"
                                            >
                                                <div className="relative w-full h-full">
                                                    <Image
                                                        src={slide.image}
                                                        alt={slide.alt}
                                                        fill
                                                        style={{ objectFit: "contain" }}
                                                        priority={index === 0}
                                                        quality={90}
                                                        className="rounded-2xl"
                                                        sizes="(max-width: 768px) 90vw, 40vw"
                                                    />
                                                </div>
                                            </motion.div>
                                        )
                                    ))}
                                </AnimatePresence>
                            </div>
                        </div>
                    </div>

                    {/* Enhanced slider navigation - Positioned outside image container */}
                    <motion.div
                        className="absolute -bottom-4 left-0 right-0 z-40"
                        initial={{ opacity: 0, y: 30 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 1.5, duration: 0.8 }}
                    >
                        <div className="flex justify-center items-center gap-2">
                            {slides.map((_, index) => (
                                <motion.button
                                    key={index}
                                    onClick={() => setCurrentSlide(index)}
                                    className={`relative rounded-full transition-all duration-500 ${index === currentSlide
                                        ? 'bg-orange-500 shadow-lg shadow-orange-400/30'
                                        : 'bg-gray-300 hover:bg-gray-400'
                                        }`}
                                    whileHover={{ scale: 1.2 }}
                                    whileTap={{ scale: 0.9 }}
                                    initial={{
                                        width: index === currentSlide ? 24 : 8,
                                        height: 8,
                                        opacity: 0,
                                        y: 20
                                    }}
                                    animate={{
                                        width: index === currentSlide ? 24 : 8,
                                        height: 8,
                                        opacity: 1,
                                        y: 0
                                    }}
                                    transition={{
                                        duration: 0.4,
                                        delay: 1.5 + index * 0.1,
                                        type: "spring",
                                        stiffness: 200
                                    }}
                                    aria-label={`Go to slide ${index + 1}`}
                                />
                            ))}
                        </div>
                    </motion.div>
                </div>
            </div>
        </section>
    );
}
