'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import PrimaryGradientBtn from '../Buttons/PrimaryGradientBtn';
import ProductCarousel from '../Products/ProductCarousel';

/**
 * API Configuration and Constants
 */
const API_BASE_URL = 'https://b2b.instinctfusionx.xyz/public/api/v1';
const CACHE_REVALIDATE_TIME = 3600; // 1 hour
const TEST_PRODUCT_KEYWORDS = ['test1', 'test2', 'test3', 'test4', 'test5'];

/**
 * Featured product categories configuration
 * These IDs correspond to specific categories in the backend
 */
const FEATURED_CATEGORIES = [
  { id: 39, name: 'Special Offers', key: 'specialOffers' },
  { id: 40, name: 'Best Sellers', key: 'bestSellers' },
  { id: 41, name: 'New Arrivals', key: 'newArrivals' }
];

/**
 * Animation variants for consistent motion effects
 */
const ANIMATION_VARIANTS = {
  fadeInUp: {
    initial: { opacity: 0, y: 30 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.6 }
  },
  staggeredFadeIn: {
    initial: { opacity: 0, y: 40 },
    animate: { opacity: 1, y: 0 }
  },
  scaleIn: {
    initial: { opacity: 0, scale: 0.9 },
    animate: { opacity: 1, scale: 1 }
  }
};

/**
 * Fetches all products for a specific category with complete pagination handling
 * @param {number} categoryId - The category ID to fetch products for
 * @returns {Promise<Array>} Array of valid, filtered products
 */
export async function getAllProductsByCategoryId(categoryId) {
  try {
    console.log(`🔍 Fetching all products for category ID ${categoryId} from API...`);

    let allProducts = [];
    let currentPage = 1;
    let hasMorePages = true;

    // Fetch all pages of products for the category
    while (hasMorePages) {
      const response = await fetch(
        `${API_BASE_URL}/products/category/${categoryId}?include_subcategories=true&page=${currentPage}`,
        {
          cache: 'force-cache',
          next: { revalidate: CACHE_REVALIDATE_TIME },
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json'
          }
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.success && data.data?.products?.data) {
        // Filter products: only active, approved, non-test products
        const validProducts = data.data.products.data.filter(product =>
          // product.is_active && // task!!! must add later
          product.approval_status === 'approved' &&
          !TEST_PRODUCT_KEYWORDS.some(keyword =>
            // product.name.toLowerCase().includes(keyword) || // task!!! must add later
            // product.slug.toLowerCase().includes(keyword) || // task!!! must add later
            product.title.toLowerCase().includes(keyword)
          )
        );

        allProducts = [...allProducts, ...validProducts];
        hasMorePages = currentPage < data.data.products.last_page;
        currentPage++;

        console.log(`✅ Fetched page ${currentPage - 1} for category ${categoryId} (${validProducts.length} valid products)`);
      } else {
        hasMorePages = false;
      }
    }

    console.log(`✅ All products fetched for category ${categoryId}: ${allProducts.length} total products`);
    return allProducts;

  } catch (error) {
    console.error(`❌ Error fetching products for category ${categoryId}:`, error);
    return [];
  }
}

// Enhanced loading skeleton component for product carousels
const ProductCarouselSkeleton = ({ sectionName }) => (
  <motion.section
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.5 }}
    className="space-y-6"
  >
    {/* Section Header Skeleton */}
    <div className="flex items-center justify-between mb-3">
      <div className="space-y-3">
        <div className="h-10 w-56 bg-gradient-to-r from-gray-200 to-gray-300 rounded-lg animate-pulse"></div>
        <div className="h-2 w-20 bg-orange-200 rounded-full animate-pulse"></div>
      </div>
      <div className="h-12 w-28 bg-gray-200 rounded-lg animate-pulse"></div>
    </div>

    {/* Product Cards Skeleton */}
    <div className="relative">
      <div className="flex gap-6 px-2 overflow-hidden">
        {[1, 2, 3, 4].map((item) => (
          <motion.div
            key={item}
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: item * 0.1, duration: 0.3 }}
            className="min-w-[320px] max-w-sm bg-white rounded-xl overflow-hidden shadow-lg border border-gray-100 hover:shadow-xl transition-shadow duration-300"
          >
            {/* Image Skeleton */}
            <div className="h-48 w-full bg-gradient-to-br from-gray-200 via-gray-100 to-gray-200 animate-pulse relative">
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-30 animate-pulse"></div>
            </div>

            {/* Content Skeleton */}
            <div className="p-6 space-y-5">
              {/* Title */}
              <div className="h-7 w-4/5 bg-gradient-to-r from-orange-200 to-orange-100 rounded animate-pulse"></div>

              {/* Description */}
              <div className="space-y-3">
                <div className="h-5 w-full bg-gray-200 rounded animate-pulse"></div>
                <div className="h-5 w-3/4 bg-gray-200 rounded animate-pulse"></div>
              </div>

              {/* Price */}
              <div className="flex items-center gap-4">
                <div className="h-9 w-24 bg-orange-200 rounded animate-pulse"></div>
                <div className="h-5 w-18 bg-gray-200 rounded animate-pulse"></div>
              </div>

              {/* Button */}
              <div className="h-14 w-full bg-gradient-to-r from-orange-200 to-orange-100 rounded-lg animate-pulse"></div>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Navigation Buttons Skeleton */}
      <div className="absolute -left-4 top-1/2 -translate-y-1/2 w-12 h-12 bg-white rounded-full shadow-lg animate-pulse"></div>
      <div className="absolute -right-4 top-1/2 -translate-y-1/2 w-12 h-12 bg-white rounded-full shadow-lg animate-pulse"></div>
    </div>
  </motion.section>
);

export default function FeaturedProducts() {
  const [bestSellers, setBestSellers] = useState([]);
  const [newArrivals, setNewArrivals] = useState([]);
  const [specialOffers, setSpecialOffers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedCategories, setSelectedCategories] = useState([
    { id: 39, name: 'Special Offers' },
    { id: 40, name: 'Best Sellers' },
    { id: 41, name: 'New Arrivals' }
  ]);

  // Fetch data on component mount
  useEffect(() => {
    async function fetchAllProducts() {
      try {
        setLoading(true);

        // Fetch products for all three sections in parallel
        const [specialOffersData, bestSellersData, newArrivalsData] = await Promise.all([
          getAllProductsByCategoryId(39), // Special Offers
          getAllProductsByCategoryId(40), // Best Sellers
          getAllProductsByCategoryId(41)  // New Arrivals
        ]);

        setSpecialOffers(specialOffersData);
        setBestSellers(bestSellersData);
        setNewArrivals(newArrivalsData);

      } catch (err) {
        console.error('❌ Error fetching featured products:', err);
        setError('Failed to load featured products');
      } finally {
        setLoading(false);
      }
    }

    fetchAllProducts();
  }, []);

  // Show loading state with enhanced design
  if (loading) {
    return (
      <section className="section-padding bg-gradient-to-b from-gray-50 via-white to-gray-50 relative overflow-hidden py-5">
        {/* Background decoration */}
        <div className="absolute inset-0 bg-gradient-to-r from-orange-50/20 to-yellow-50/20"></div>
        <div className="absolute top-0 left-1/4 w-96 h-96 bg-orange-100/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-yellow-100/10 rounded-full blur-3xl"></div>

        <div className="container-custom px-2 sm:px-4 lg:px-6 relative z-10">
          {/* Enhanced Header */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center space-y-6 mb-5"
          >
            <motion.span
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.2, duration: 0.5 }}
              className="inline-block text-sm sm:text-base font-bold text-orange-600 uppercase tracking-wide bg-orange-50 px-6 py-3 rounded-full border border-orange-200"
            >
              ✨ Discover Quality Products
            </motion.span>

            <motion.h2
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3, duration: 0.6 }}
              className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-extrabold bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 bg-clip-text text-transparent leading-tight tracking-tight"
            >
              Trending Products
            </motion.h2>

            <motion.div
              initial={{ width: 0 }}
              animate={{ width: "6rem" }}
              transition={{ delay: 0.5, duration: 0.8 }}
              className="h-2 bg-gradient-to-r from-orange-400 to-yellow-400 mx-auto rounded-full shadow-lg"
            ></motion.div>

            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6, duration: 0.6 }}
              className="text-lg sm:text-xl text-gray-700 max-w-3xl sm:max-w-4xl mx-auto px-4 sm:px-0 leading-relaxed font-medium"
            >
              Explore our handpicked selection of top-performing products loved by our customers worldwide
            </motion.p>
          </motion.div>

          {/* Loading Skeletons */}
          <div className="space-y-12 sm:space-y-14">
            <ProductCarouselSkeleton sectionName="Special Offers" />
            <ProductCarouselSkeleton sectionName="Best Sellers" />
            <ProductCarouselSkeleton sectionName="New Arrivals" />
          </div>
        </div>
      </section>
    );
  }

  // Show error state (but don't break the layout)
  if (error) {
    console.error('FeaturedProducts error:', error);
    return null; // Hide component on error
  }

  return (
    <section className="section-padding bg-gradient-to-b from-gray-50 via-white to-gray-50 relative overflow-hidden py-5">
      {/* Enhanced Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-r from-orange-50/20 to-yellow-50/20"></div>
      <div className="absolute top-0 left-1/4 w-96 h-96 bg-orange-100/10 rounded-full blur-3xl animate-pulse"></div>
      <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-yellow-100/10 rounded-full blur-3xl animate-pulse"></div>

      <div className="container-custom px-2 sm:px-4 lg:px-6 relative z-10">
        {/* Enhanced Header Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center space-y-6 mb-5"
        >
          <motion.span
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.2, duration: 0.5 }}
            className="inline-block text-sm sm:text-base font-bold text-orange-600 uppercase tracking-wide bg-orange-50 px-6 py-3 rounded-full border border-orange-200 shadow-sm hover:shadow-md transition-shadow duration-300"
          >
            ✨ Discover Quality Products
          </motion.span>

          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3, duration: 0.6 }}
            className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-extrabold bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 bg-clip-text text-transparent leading-tight tracking-tight"
          >
            Trending Products
          </motion.h2>

          <motion.div
            initial={{ width: 0 }}
            animate={{ width: "6rem" }}
            transition={{ delay: 0.5, duration: 0.8 }}
            className="h-2 bg-gradient-to-r from-orange-400 to-yellow-400 mx-auto rounded-full shadow-lg"
          ></motion.div>

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6, duration: 0.6 }}
            className="text-lg sm:text-xl text-gray-700 max-w-3xl sm:max-w-4xl mx-auto px-4 sm:px-0 leading-relaxed font-medium"
          >
            Explore our handpicked selection of top-performing products loved by our customers worldwide
          </motion.p>
        </motion.div>

        {/* Product Carousels with Enhanced Styling */}
        <div className="space-y-12 sm:space-y-14">
          <motion.div
            initial={{ opacity: 0, y: 40 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.8, duration: 0.6 }}
            className="overflow-hidden rounded-2xl bg-blue-50/30 backdrop-blur-sm border border-blue-100 shadow-2xl p-2 sm:p-4 hover:shadow-2xl transition-all duration-500"
          >
            <ProductCarousel
              products={specialOffers}
              category={selectedCategories[0]}
            />
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 40 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 1.0, duration: 0.6 }}
            className="overflow-hidden rounded-2xl bg-blue-50/30 backdrop-blur-sm border border-blue-100 shadow-2xl p-2 sm:p-4 hover:shadow-2xl transition-all duration-500"
          >
            <ProductCarousel
              products={bestSellers}
              category={selectedCategories[1]}
            />
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 40 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 1.2, duration: 0.6 }}
            className="overflow-hidden rounded-2xl bg-blue-50/30 backdrop-blur-sm border border-blue-100 shadow-2xl p-2 sm:p-4 hover:shadow-2xl transition-all duration-500"
          >
            <ProductCarousel
              products={newArrivals}
              category={selectedCategories[2]}
            />
          </motion.div>
        </div>

        {/* Enhanced Call-to-Action */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1.4, duration: 0.6 }}
          className="text-center mt-12 sm:mt-14"
        >
          <div className="inline-block p-1 bg-gradient-to-r from-orange-400 to-yellow-400 rounded-2xl shadow-lg hover:shadow-xl transition-shadow duration-300">
            <PrimaryGradientBtn
              text="View All Products"
              navLink="/products"
              tailwindCss="w-full sm:w-auto px-10 py-5 text-lg sm:text-xl font-bold bg-white hover:bg-gray-50 text-gray-900 rounded-xl border-0 shadow-none hover:shadow-md transition-all duration-300 tracking-wide"
            />
          </div>

          <motion.p
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1.6, duration: 0.5 }}
            className="mt-4 text-base font-medium text-gray-600 leading-relaxed"
          >
            Discover thousands of quality products from trusted suppliers
          </motion.p>
        </motion.div>
      </div>
    </section>
  );
}