import React from 'react'
import Image from 'next/image';
import Link from 'next/link';
import { motion } from 'framer-motion';
import {
    FaEnvelope,
    FaPhone,
    FaMapMarkerAlt,
    FaCheckCircle,
    FaTimesCircle,
    FaEye
} from 'react-icons/fa';

export default function PublicPartnerCard({ partner, index, viewMode = 'grid' }) {
    // Sanitize partner data
    const sanitizedPartner = {
        id: partner?.id || '',
        name: partner?.name || 'Unknown Partner',
        email: partner?.email || '',
        phone: partner?.phone || '',
        address: partner?.address || '',
        vendor_profile: partner?.vendor_profile || null
    };

    const companyName = sanitizedPartner.vendor_profile?.company_name || sanitizedPartner.name;
    const companyDescription = sanitizedPartner.vendor_profile?.company_description || '';
    const companyLogo = sanitizedPartner.vendor_profile?.company_logo;
    const isApproved = sanitizedPartner.vendor_profile?.is_approved || false;

    const cardVariants = {
        hidden: { opacity: 0, y: 20 },
        visible: {
            opacity: 1,
            y: 0,
            transition: { delay: index * 0.1, duration: 0.3 }
        }
    };

    // List View
    if (viewMode === 'list') {
        return (
            <motion.div
                variants={cardVariants}
                initial="hidden"
                animate="visible"
                className="bg-white rounded-xl shadow-sm hover:shadow-lg transition-all duration-300 p-6 border border-gray-200"
            >
                <div className="flex flex-col md:flex-row md:items-center gap-4">
                    {/* Partner Logo/Avatar */}
                    <div className="flex-shrink-0">
                        <div className="relative w-16 h-16 md:w-20 md:h-20">
                            {companyLogo ? (
                                <Image
                                    src={`https://b2b.instinctfusionx.xyz/public${companyLogo}`}
                                    alt={companyName}
                                    fill
                                    className="object-cover rounded-lg"
                                    sizes="80px"
                                    onError={(e) => {
                                        e.target.style.display = 'none';
                                        e.target.nextSibling.style.display = 'flex';
                                    }}
                                />
                            ) : (
                                <div className="w-full h-full bg-gradient-to-br from-orange-400 to-orange-600 rounded-lg flex items-center justify-center">
                                    <span className="text-white font-bold text-lg">
                                        {companyName.charAt(0).toUpperCase()}
                                    </span>
                                </div>
                            )}
                        </div>
                    </div>

                    {/* Partner Info */}
                    <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between mb-2">
                            <div>
                                <h3 className="text-lg font-semibold text-gray-900 truncate">
                                    {companyName}
                                </h3>
                                <div className="flex items-center gap-2 mt-1">
                                    {isApproved ? (
                                        <span className="inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            <FaCheckCircle className="text-xs" />
                                            Verified
                                        </span>
                                    ) : (
                                        <span className="inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-600">
                                            <FaTimesCircle className="text-xs" />
                                            Pending
                                        </span>
                                    )}
                                </div>
                            </div>
                        </div>

                        {companyDescription && (
                            <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                                {companyDescription}
                            </p>
                        )}

                        <div className="grid grid-cols-1 md:grid-cols-3 gap-2 text-sm text-gray-500">
                            {sanitizedPartner.email && (
                                <div className="flex items-center gap-2">
                                    <FaEnvelope className="text-xs text-orange-500" />
                                    <span className="truncate">{sanitizedPartner.email}</span>
                                </div>
                            )}
                            {sanitizedPartner.phone && (
                                <div className="flex items-center gap-2">
                                    <FaPhone className="text-xs text-orange-500" />
                                    <span>{sanitizedPartner.phone}</span>
                                </div>
                            )}
                            {sanitizedPartner.address && (
                                <div className="flex items-center gap-2">
                                    <FaMapMarkerAlt className="text-xs text-orange-500" />
                                    <span className="truncate">{sanitizedPartner.address}</span>
                                </div>
                            )}
                        </div>
                    </div>

                    {/* Action Button */}
                    <div className="flex-shrink-0">
                        <Link href={`/partners/${sanitizedPartner.id}`}>
                            <motion.button
                                whileHover={{ scale: 1.05 }}
                                whileTap={{ scale: 0.95 }}
                                className="px-4 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors duration-200 flex items-center gap-2"
                            >
                                <FaEye className="text-sm" />
                                View
                            </motion.button>
                        </Link>
                    </div>
                </div>
            </motion.div>
        );
    }


    // Grid View
    return (
        <motion.div
            variants={cardVariants}
            initial="hidden"
            animate="visible"
            className="bg-white rounded-xl shadow-sm hover:shadow-lg transition-all duration-300 p-6 border border-gray-200 group h-full flex flex-col"
        >
            {/* Partner Logo/Avatar */}
            <div className="relative mb-4 aspect-square">
                {companyLogo ? (
                    <Image
                        src={`https://b2b.instinctfusionx.xyz/public${companyLogo}`}
                        alt={companyName}
                        fill
                        className="object-cover rounded-lg"
                        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"
                        onError={(e) => {
                            e.target.style.display = 'none';
                            e.target.nextSibling.style.display = 'flex';
                        }}
                    />
                ) : (
                    <div className="w-full h-full bg-gradient-to-br from-orange-400 to-orange-600 rounded-lg flex items-center justify-center">
                        <span className="text-white font-bold text-2xl">
                            {companyName.charAt(0).toUpperCase()}
                        </span>
                    </div>
                )}
            </div>

            {/* Partner Info */}
            <div className="flex-1 flex flex-col">
                <div className="mb-3">
                    <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-1">
                        {companyName}
                    </h3>
                    <div className="flex justify-center mb-3">
                        {isApproved ? (
                            <span className="inline-flex items-center gap-1 px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                <FaCheckCircle className="text-xs" />
                                Verified
                            </span>
                        ) : (
                            <span className="inline-flex items-center gap-1 px-3 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-600">
                                <FaTimesCircle className="text-xs" />
                                Pending
                            </span>
                        )}
                    </div>
                </div>

                {companyDescription && (
                    <p className="text-gray-600 text-sm mb-4 line-clamp-3 flex-1">
                        {companyDescription}
                    </p>
                )}

                <div className="space-y-2 text-sm text-gray-500 mb-4">
                    {sanitizedPartner.email && (
                        <div className="flex items-center gap-2">
                            <FaEnvelope className="text-xs text-orange-500 flex-shrink-0" />
                            <span className="truncate">{sanitizedPartner.email}</span>
                        </div>
                    )}
                    {sanitizedPartner.phone && (
                        <div className="flex items-center gap-2">
                            <FaPhone className="text-xs text-orange-500 flex-shrink-0" />
                            <span>{sanitizedPartner.phone}</span>
                        </div>
                    )}
                    {sanitizedPartner.address && (
                        <div className="flex items-center gap-2">
                            <FaMapMarkerAlt className="text-xs text-orange-500 flex-shrink-0" />
                            <span className="truncate">{sanitizedPartner.address}</span>
                        </div>
                    )}
                </div>

                {/* Action Button */}
                <div className="mt-auto">
                    <Link href={`/partners/${sanitizedPartner.id}`} className="block">
                        <motion.button
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                            className="w-full px-4 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors duration-200 flex items-center justify-center gap-2"
                        >
                            <FaEye className="text-sm" />
                            View Details
                        </motion.button>
                    </Link>
                </div>
            </div>
        </motion.div>
    );
}
