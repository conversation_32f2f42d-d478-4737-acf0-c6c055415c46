'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import {
    FaSearch,
    <PERSON>aTh,
    FaList,
    FaChevronLeft,
    FaChevronRight,
    FaBuilding,
    FaTimesCircle,
    FaSortUp,
    FaSortDown
} from 'react-icons/fa';
import toast from 'react-hot-toast';
import PublicPartnerCard from '../Cards/PublicPartnerCard';

// API function to fetch partners/vendors
// Note: This fetches all partners and filters client-side. For better performance and accurate pagination,
// consider adding server-side filtering for approved partners only.
export async function getAllPartners(page = 1, perPage = 12, search = '', sortBy = 'name', sortOrder = 'asc') {
    try {
        // Fetch more items per page to account for client-side filtering
        // This ensures we have enough approved partners to display
        const adjustedPerPage = Math.max(perPage * 2, 50); // Fetch at least 50 items to increase chances of having approved partners

        const params = new URLSearchParams({
            page: page.toString(),
            per_page: adjustedPerPage.toString(),
        });

        if (search.trim()) {
            params.append('search', search.trim());
        }

        if (sortBy) {
            params.append('sort', sortBy);
            params.append('order', sortOrder);
        }

        const response = await fetch(`https://b2b.instinctfusionx.xyz/public/api/v1/vendors?${params.toString()}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            },
            cache: 'force-cache',
            next: { revalidate: 3600 }
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log('🔍 Partners API Response:', data);

        if (data.success && data.data && data.data.vendors) {
            return data.data.vendors;
        } else {
            throw new Error('Invalid response structure');
        }
    } catch (error) {
        console.error('❌ Error fetching partners:', error);
        throw error;
    }
}

// Loading Skeleton Component
const PartnerSkeleton = ({ viewMode = 'grid' }) => {
    if (viewMode === 'list') {
        return (
            <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200 animate-pulse">
                <div className="flex flex-col md:flex-row md:items-center gap-4">
                    <div className="flex-shrink-0">
                        <div className="w-16 h-16 md:w-20 md:h-20 bg-gray-200 rounded-lg"></div>
                    </div>
                    <div className="flex-1 min-w-0">
                        <div className="h-6 bg-gray-200 rounded mb-2 w-3/4"></div>
                        <div className="h-4 bg-gray-200 rounded mb-3 w-1/2"></div>
                        <div className="h-4 bg-gray-200 rounded mb-2 w-full"></div>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
                            <div className="h-4 bg-gray-200 rounded"></div>
                            <div className="h-4 bg-gray-200 rounded"></div>
                            <div className="h-4 bg-gray-200 rounded"></div>
                        </div>
                    </div>
                    <div className="flex-shrink-0">
                        <div className="w-20 h-10 bg-gray-200 rounded-lg"></div>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200 animate-pulse h-full flex flex-col">
            <div className="aspect-square bg-gray-200 rounded-lg mb-4"></div>
            <div className="flex-1 flex flex-col">
                <div className="h-6 bg-gray-200 rounded mb-2"></div>
                <div className="h-4 bg-gray-200 rounded mb-3 w-1/2 mx-auto"></div>
                <div className="h-4 bg-gray-200 rounded mb-2"></div>
                <div className="h-4 bg-gray-200 rounded mb-2"></div>
                <div className="h-4 bg-gray-200 rounded mb-4"></div>
                <div className="mt-auto">
                    <div className="h-10 bg-gray-200 rounded-lg"></div>
                </div>
            </div>
        </div>
    );
};

// Main AllPartners Component
export default function AllPartners() {
    const [partners, setPartners] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [pagination, setPagination] = useState(null);
    const [currentPage, setCurrentPage] = useState(1);
    const [searchQuery, setSearchQuery] = useState('');
    const [sortBy, setSortBy] = useState('name');
    const [sortOrder, setSortOrder] = useState('asc');
    const [viewMode, setViewMode] = useState('grid');
    const [perPage, setPerPage] = useState(12);

    const router = useRouter();
    const searchParams = useSearchParams();

    // Initialize state from URL parameters
    useEffect(() => {
        const page = parseInt(searchParams.get('page')) || 1;
        const search = searchParams.get('search') || '';
        const sort = searchParams.get('sort') || 'name';
        const order = searchParams.get('order') || 'asc';
        const view = searchParams.get('view') || 'grid';
        const limit = parseInt(searchParams.get('per_page')) || 12;

        setCurrentPage(page);
        setSearchQuery(search);
        setSortBy(sort);
        setSortOrder(order);
        setViewMode(view);
        setPerPage(limit);
    }, [searchParams]);

    // Fetch partners data
    const fetchPartners = useCallback(async () => {
        setLoading(true);
        setError(null);

        try {
            console.log('🔄 Fetching partners:', { currentPage, perPage, searchQuery, sortBy, sortOrder });

            const data = await getAllPartners(currentPage, perPage, searchQuery, sortBy, sortOrder);

            if (data && Array.isArray(data.data)) {
                // Filter partners to only include approved ones
                const approvedPartners = data.data.filter(partner => {
                    // Use optional chaining to safely access vendor_profile.is_approved
                    return partner?.vendor_profile?.is_approved === true;
                });

                console.log('🔍 Filtering partners:', {
                    totalReceived: data.data.length,
                    approvedCount: approvedPartners.length,
                    filteredOut: data.data.length - approvedPartners.length
                });

                // Update pagination metadata to reflect only approved partners
                const updatedPagination = {
                    ...data,
                    // Update the data array with filtered partners
                    data: approvedPartners,
                    // Recalculate pagination metadata based on approved partners
                    total: approvedPartners.length,
                    to: Math.min(data.from + approvedPartners.length - 1, approvedPartners.length),
                    // Note: This is a simplified approach. In a real-world scenario,
                    // the filtering should ideally be done on the server-side for accurate pagination
                };

                setPartners(approvedPartners);
                setPagination(updatedPagination);

                console.log('✅ Approved partners loaded successfully:', approvedPartners.length, 'partners');

                if (approvedPartners.length === 0 && data.data.length > 0) {
                    console.log('⚠️ No approved partners found in current page, but unapproved partners exist');
                }
            } else {
                throw new Error('Invalid data structure received');
            }
        } catch (err) {
            console.error('❌ Error fetching partners:', err);
            setError(err.message);
            toast.error('Failed to load partners. Please try again.');
        } finally {
            setLoading(false);
        }
    }, [currentPage, perPage, searchQuery, sortBy, sortOrder]);

    // Fetch data when dependencies change
    useEffect(() => {
        fetchPartners();
    }, [fetchPartners]);

    // Handle page change
    const handlePageChange = useCallback((page) => {
        if (!page || page < 1) page = 1;
        if (page === currentPage) return;
        if (pagination && page > pagination.last_page) page = pagination.last_page;

        setCurrentPage(page);
        const params = new URLSearchParams(searchParams);
        params.set('page', page.toString());
        router.push(`?${params.toString()}`);
    }, [router, searchParams, currentPage, pagination]);

    // Handle search
    const handleSearch = useCallback((e) => {
        e.preventDefault();
        const params = new URLSearchParams(searchParams);
        if (searchQuery.trim()) {
            params.set('search', searchQuery.trim());
        } else {
            params.delete('search');
        }
        params.set('page', '1');
        router.push(`?${params.toString()}`);
    }, [router, searchParams, searchQuery]);

    // Handle sort change
    const handleSortChange = useCallback((newSortBy) => {
        const newSortOrder = sortBy === newSortBy && sortOrder === 'asc' ? 'desc' : 'asc';
        const params = new URLSearchParams(searchParams);
        params.set('sort', newSortBy);
        params.set('order', newSortOrder);
        params.set('page', '1');
        router.push(`?${params.toString()}`);
    }, [router, searchParams, sortBy, sortOrder]);

    // Handle view mode change
    const handleViewModeChange = useCallback((newViewMode) => {
        const params = new URLSearchParams(searchParams);
        params.set('view', newViewMode);
        router.push(`?${params.toString()}`);
    }, [router, searchParams]);

    // Handle per page change
    const handlePerPageChange = useCallback((newPerPage) => {
        const params = new URLSearchParams(searchParams);
        params.set('per_page', newPerPage.toString());
        params.set('page', '1');
        router.push(`?${params.toString()}`);
    }, [router, searchParams]);

    return (
        <div className="min-h-screen bg-gray-50 text-gray-600">
            <div className="container mx-auto px-4 py-8">
                {/* Header */}
                <div className="mb-8">
                    <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Our Approved Partners</h1>
                    <p className="text-gray-600 max-w-2xl">
                        Discover our trusted network of verified and approved partners who help us deliver quality products and services.
                    </p>
                </div>

                {/* Search and Filters */}
                <div className="bg-white rounded-xl shadow-sm p-6 mb-8 border border-gray-200">
                    <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between">
                        {/* Search */}
                        <form onSubmit={handleSearch} className="flex-1 max-w-md">
                            <div className="relative">
                                <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                                <input
                                    type="text"
                                    placeholder="Search partners..."
                                    value={searchQuery}
                                    onChange={(e) => setSearchQuery(e.target.value)}
                                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors"
                                />
                            </div>
                        </form>

                        {/* Controls */}
                        <div className="flex flex-wrap items-center gap-4">
                            {/* Sort */}
                            <div className="flex items-center gap-2">
                                <span className="text-sm text-gray-600">Sort by:</span>
                                <div className="flex gap-1">
                                    <button
                                        onClick={() => handleSortChange('name')}
                                        className={`px-3 py-1 rounded text-sm flex items-center gap-1 transition-colors ${sortBy === 'name'
                                            ? 'bg-orange-100 text-orange-700'
                                            : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                                            }`}
                                    >
                                        Name
                                        {sortBy === 'name' && (
                                            sortOrder === 'asc' ? <FaSortUp /> : <FaSortDown />
                                        )}
                                    </button>
                                    <button
                                        onClick={() => handleSortChange('created_at')}
                                        className={`px-3 py-1 rounded text-sm flex items-center gap-1 transition-colors ${sortBy === 'created_at'
                                            ? 'bg-orange-100 text-orange-700'
                                            : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                                            }`}
                                    >
                                        Date
                                        {sortBy === 'created_at' && (
                                            sortOrder === 'asc' ? <FaSortUp /> : <FaSortDown />
                                        )}
                                    </button>
                                </div>
                            </div>

                            {/* View Mode */}
                            <div className="flex items-center gap-1 bg-gray-100 rounded-lg p-1">
                                <button
                                    onClick={() => handleViewModeChange('grid')}
                                    className={`p-2 rounded transition-colors ${viewMode === 'grid'
                                        ? 'bg-white text-orange-600 shadow-sm'
                                        : 'text-gray-600 hover:text-gray-800'
                                        }`}
                                    title="Grid View"
                                >
                                    <FaTh />
                                </button>
                                <button
                                    onClick={() => handleViewModeChange('list')}
                                    className={`p-2 rounded transition-colors ${viewMode === 'list'
                                        ? 'bg-white text-orange-600 shadow-sm'
                                        : 'text-gray-600 hover:text-gray-800'
                                        }`}
                                    title="List View"
                                >
                                    <FaList />
                                </button>
                            </div>

                            {/* Per Page */}
                            <select
                                value={perPage}
                                onChange={(e) => handlePerPageChange(parseInt(e.target.value))}
                                className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                            >
                                <option value={12}>12 per page</option>
                                <option value={24}>24 per page</option>
                                <option value={48}>48 per page</option>
                            </select>
                        </div>
                    </div>
                </div>

                {/* Loading State */}
                {loading && (
                    <div className="space-y-6">
                        {viewMode === 'grid' ? (
                            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                                {[...Array(perPage)].map((_, index) => (
                                    <PartnerSkeleton key={index} viewMode="grid" />
                                ))}
                            </div>
                        ) : (
                            <div className="space-y-4">
                                {[...Array(perPage)].map((_, index) => (
                                    <PartnerSkeleton key={index} viewMode="list" />
                                ))}
                            </div>
                        )}
                    </div>
                )}

                {/* Error State */}
                {error && !loading && (
                    <div className="text-center py-12">
                        <div className="bg-red-50 border border-red-200 rounded-xl p-8 max-w-md mx-auto">
                            <div className="text-red-500 mb-4">
                                <FaTimesCircle className="text-4xl mx-auto" />
                            </div>
                            <h3 className="text-lg font-semibold text-red-800 mb-2">Error Loading Partners</h3>
                            <p className="text-red-600 mb-4">{error}</p>
                            <button
                                onClick={fetchPartners}
                                className="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors duration-200"
                            >
                                Try Again
                            </button>
                        </div>
                    </div>
                )}

                {/* Empty State */}
                {!loading && !error && partners.length === 0 && (
                    <div className="text-center py-12">
                        <div className="bg-gray-50 border border-gray-200 rounded-xl p-8 max-w-md mx-auto">
                            <div className="text-gray-400 mb-4">
                                <FaBuilding className="text-4xl mx-auto" />
                            </div>
                            <h3 className="text-lg font-semibold text-gray-800 mb-2">No Approved Partners Found</h3>
                            <p className="text-gray-600 mb-4">
                                {searchQuery ?
                                    `No approved partners match your search for "${searchQuery}".` :
                                    'No approved partners are currently available.'
                                }
                            </p>
                            {searchQuery && (
                                <button
                                    onClick={() => {
                                        setSearchQuery('');
                                        const params = new URLSearchParams(searchParams);
                                        params.delete('search');
                                        params.set('page', '1');
                                        router.push(`?${params.toString()}`);
                                    }}
                                    className="px-4 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors duration-200"
                                >
                                    Clear Search
                                </button>
                            )}
                        </div>
                    </div>
                )}

                {/* Partners Display */}
                {!loading && !error && partners.length > 0 && (
                    <>
                        {/* Results Info */}
                        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6 gap-4">
                            <div className="text-sm text-gray-600">
                                Showing {pagination?.from || 1} to {pagination?.to || partners.length} of {pagination?.total || partners.length} approved partners
                            </div>
                            {searchQuery && (
                                <div className="text-sm text-gray-600">
                                    Approved partners matching: <span className="font-medium">"{searchQuery}"</span>
                                </div>
                            )}
                        </div>

                        {/* Partners Grid/List */}
                        <AnimatePresence mode="wait">
                            {viewMode === 'grid' ? (
                                <motion.div
                                    key="grid"
                                    initial={{ opacity: 0 }}
                                    animate={{ opacity: 1 }}
                                    exit={{ opacity: 0 }}
                                    className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8"
                                >
                                    {partners.map((partner, index) => (
                                        <PublicPartnerCard
                                            key={partner.id}
                                            partner={partner}
                                            index={index}
                                            viewMode="grid"
                                        />
                                    ))}
                                </motion.div>
                            ) : (
                                <motion.div
                                    key="list"
                                    initial={{ opacity: 0 }}
                                    animate={{ opacity: 1 }}
                                    exit={{ opacity: 0 }}
                                    className="space-y-4 mb-8"
                                >
                                    {partners.map((partner, index) => (
                                        <PublicPartnerCard
                                            key={partner.id}
                                            partner={partner}
                                            index={index}
                                            viewMode="list"
                                        />
                                    ))}
                                </motion.div>
                            )}
                        </AnimatePresence>

                        {/* Pagination */}
                        {pagination && pagination.last_page > 1 && (
                            <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
                                <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
                                    {/* Pagination Info */}
                                    <div className="text-sm text-gray-500">
                                        Showing {pagination.from || 1} to {pagination.to || pagination.per_page} of {pagination.total || 0} approved partners
                                        <span className="ml-4 text-xs text-gray-400">
                                            (Page {currentPage} of {pagination.last_page})
                                        </span>
                                    </div>

                                    {/* Pagination Controls */}
                                    <div className="flex items-center gap-2">
                                        {/* Previous Button */}
                                        <button
                                            onClick={() => handlePageChange(currentPage - 1)}
                                            disabled={currentPage <= 1 || !pagination.prev_page_url}
                                            className="px-4 py-2 rounded-xl bg-white border border-gray-200 text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2 transition-all"
                                        >
                                            <FaChevronLeft className="text-xs" />
                                            Previous
                                        </button>

                                        {/* Page Numbers */}
                                        <div className="hidden sm:flex items-center gap-1">
                                            {/* First page */}
                                            {currentPage > 3 && (
                                                <>
                                                    <button
                                                        onClick={() => handlePageChange(1)}
                                                        className="px-3 py-2 rounded-lg text-sm hover:bg-gray-100 transition-colors"
                                                    >
                                                        1
                                                    </button>
                                                    {currentPage > 4 && (
                                                        <span className="px-2 text-gray-400">...</span>
                                                    )}
                                                </>
                                            )}

                                            {/* Current page and neighbors */}
                                            {Array.from({ length: Math.min(5, pagination.last_page) }, (_, i) => {
                                                const pageNum = Math.max(1, Math.min(
                                                    currentPage - 2 + i,
                                                    pagination.last_page - 4 + i
                                                ));

                                                if (pageNum > pagination.last_page) return null;

                                                return (
                                                    <button
                                                        key={pageNum}
                                                        onClick={() => handlePageChange(pageNum)}
                                                        className={`px-3 py-2 rounded-lg text-sm transition-colors ${pageNum === currentPage
                                                            ? 'bg-orange-500 text-white'
                                                            : 'hover:bg-gray-100'
                                                            }`}
                                                    >
                                                        {pageNum}
                                                    </button>
                                                );
                                            })}

                                            {/* Last page */}
                                            {currentPage < pagination.last_page - 2 && (
                                                <>
                                                    {currentPage < pagination.last_page - 3 && (
                                                        <span className="px-2 text-gray-400">...</span>
                                                    )}
                                                    <button
                                                        onClick={() => handlePageChange(pagination.last_page)}
                                                        className="px-3 py-2 rounded-lg text-sm hover:bg-gray-100 transition-colors"
                                                    >
                                                        {pagination.last_page}
                                                    </button>
                                                </>
                                            )}
                                        </div>

                                        {/* Next Button */}
                                        <button
                                            onClick={() => handlePageChange(currentPage + 1)}
                                            disabled={currentPage >= pagination.last_page || !pagination.next_page_url}
                                            className="px-4 py-2 rounded-xl bg-white border border-gray-200 text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2 transition-all"
                                        >
                                            Next
                                            <FaChevronRight className="text-xs" />
                                        </button>
                                    </div>
                                </div>
                            </div>
                        )}
                    </>
                )}
            </div>
        </div>
    );
}
