'use client';

import { useState } from 'react';
import Link from 'next/link';
import { BiSolidOffer } from "react-icons/bi";
import { HiBadgeCheck } from "react-icons/hi";
import { MdCategory } from "react-icons/md";
import { usePathname } from 'next/navigation';
import {
  HomeIcon, ChartBarIcon, UsersIcon,
  ShoppingCartIcon, Cog6ToothIcon, ArrowLeftOnRectangleIcon, ShoppingBagIcon, PlusCircleIcon, ListBulletIcon,
  ChevronDownIcon, EnvelopeIcon, DocumentTextIcon, Bars3BottomLeftIcon, MagnifyingGlassIcon, EyeIcon, CogIcon, LinkIcon, GlobeAltIcon
} from '@heroicons/react/24/outline';
import { IoNotificationsSharp, IoSettings } from "react-icons/io5";
import { FaBoxes } from "react-icons/fa";
import { FaUserAlt } from "react-icons/fa";
import Logout from '../Authentication/Logout';
import { RiUserSettingsFill } from 'react-icons/ri';

const superAdminMenuItems = [
  { name: 'Dashboard', icon: HomeIcon, path: '/super-admin' },
  { name: 'Analytics', icon: ChartBarIcon, path: '/super-admin/analytics' },
  { name: 'Bulk Orders', icon: FaBoxes, path: '/super-admin/bulk-orders' },
  { name: 'Customers', icon: UsersIcon, path: '/super-admin/customers' },
  { name: 'Manage Users', icon: UsersIcon, path: '/super-admin/manage-users' },
  { name: 'Manage Brands & Categories', icon: MdCategory, path: '/super-admin/manage-brands-categories' },
  { name: 'Newsletter Subscribers', icon: EnvelopeIcon, path: '/super-admin/newsletter-subscribers' },
  { name: 'Notifications', icon: IoNotificationsSharp, path: '/super-admin/notifications' },
  { name: 'Orders', icon: ShoppingCartIcon, path: '/super-admin/orders' },
  {
    name: 'Products',
    icon: ShoppingBagIcon,
    children: [
      { name: 'Add New Product', icon: PlusCircleIcon, path: '/super-admin/products/add' },
      { name: 'Approve Products', icon: HiBadgeCheck, path: '/super-admin/products/approve' },
      { name: 'Manage Products', icon: ListBulletIcon, path: '/super-admin/products/manage' },
    ]
  },
  { name: 'Product Ranking', icon: Bars3BottomLeftIcon, path: '/super-admin/ranking' },
  { name: 'Assign Categories', icon: MdCategory, path: '/super-admin/assign-category' },
  { name: 'Generate Manual Invoice', icon: DocumentTextIcon, path: '/super-admin/generate-manual-invoice' },
  { name: 'Settings', icon: RiUserSettingsFill, path: '/super-admin/settings' },
  { name: 'Software', icon: IoSettings, path: '/super-admin/software' }
];

const partnerAdminMenuItems = [
  { name: 'Dashboard', icon: HomeIcon, path: '/partner-admin' },
  { name: 'Analytics', icon: ChartBarIcon, path: '/partner-admin/analytics' },
  { name: 'Notifications', icon: IoNotificationsSharp, path: '/partner-admin/notifications' },
  { name: 'Special Offers', icon: BiSolidOffer, path: '/partner-admin/special-offers' },
  { name: 'Orders', icon: ShoppingCartIcon, path: '/partner-admin/orders' },
  {
    name: 'Products',
    icon: ShoppingBagIcon,
    children: [
      { name: 'Add New Product', icon: PlusCircleIcon, path: '/partner-admin/products/add' },
      { name: 'Multiple Product Upload', icon: FaBoxes, path: '/partner-admin/products/add-multi-products' },
      { name: 'Manage Products', icon: ListBulletIcon, path: '/partner-admin/products/manage' },
    ]
  },
];

const customerAdminMenuItems = [
  { name: 'Dashboard', icon: HomeIcon, path: '/customer-admin' },
  { name: 'Analytics', icon: ChartBarIcon, path: '/customer-admin/analytics' },
  { name: 'Notifications', icon: IoNotificationsSharp, path: '/customer-admin/notifications' },
  /* { name: 'Special Offers', icon: BiSolidOffer, path: '/customer-admin/special-offers' }, */
  { name: 'Orders', icon: ShoppingCartIcon, path: '/customer-admin/orders' },
  /* {
    name: 'Products',
    icon: ShoppingBagIcon,
    children: [
      { name: 'Add New Product', icon: PlusCircleIcon, path: '/customer-admin/products/add' },
      { name: 'Manage Products', icon: ListBulletIcon, path: '/customer-admin/products/manage' },
    ]
  }, */
];

const seoAdminMenuItems = [
  { name: 'Dashboard', icon: HomeIcon, path: '/seo-admin' },
  { name: 'SEO Analytics', icon: ChartBarIcon, path: '/seo-admin/analytics' },
  { name: 'Keyword Research', icon: MagnifyingGlassIcon, path: '/seo-admin/keyword-research' },
  { name: 'Meta Management', icon: DocumentTextIcon, path: '/seo-admin/meta-management' },
  { name: 'Content Optimization', icon: EyeIcon, path: '/seo-admin/content-optimization' },
  { name: 'Site Audit', icon: CogIcon, path: '/seo-admin/site-audit' },
  { name: 'Backlink Management', icon: LinkIcon, path: '/seo-admin/backlinks' },
  { name: 'Sitemap Manager', icon: GlobeAltIcon, path: '/seo-admin/sitemap' },
];

export default function Sidebar({ collapsed }) {
  const router = usePathname();
  const [activeItem, setActiveItem] = useState('Dashboard');
  const [openDropdowns, setOpenDropdowns] = useState({});

  const [menuItems, setMenuItems] = useState(
    (router.includes('super-admin') && superAdminMenuItems) ||
    (router.includes('partner-admin') && partnerAdminMenuItems) ||
    (router.includes('customer-admin') && customerAdminMenuItems) ||
    (router.includes('seo-admin') && seoAdminMenuItems)
  );

  const toggleDropdown = (name) => {
    setOpenDropdowns(prev => ({
      ...prev,
      [name]: !prev[name]
    }));
  };

  return (
    <aside
      className={`bg-gray-800 text-white transition-all duration-300 ease-in-out relative ${collapsed ? 'w-16' : 'w-60'
        } fixed h-full z-10 md:relative md:translate-x-0`}>
      {/* title */}
      <div className="flex items-center justify-between h-[62px] px-5 border-b border-gray-700">
        {/* {!collapsed && (
          <span className="text-xl font-semibold">{router.includes('super-admin') ? 'Super Admin' : 'Partner Admin'}</span>
        )} */}

        {!collapsed && (<span className="text-xl font-semibold">
          {router.includes('super-admin') && "Super Admin"}
          {router.includes('partner-admin') && "Partner Admin"}
          {router.includes('customer-admin') && "Customer Dashboard"}
          {router.includes('seo-admin') && "SEO Expert"}
        </span>)}

        {collapsed && (
          <span className="text-xl font-semibold mx-auto">AP</span>
        )}
      </div>

      <nav className="mt-6 px-1 font-semibold max-h-[calc(100dvh-10rem)] overflow-y-scroll">
        <ul>
          {/* Menu items  */}
          {menuItems.map((item) => (
            <li key={item.name} className="mb-2">
              {/* Submenus */}
              {item.children ? (
                <div>
                  <button
                    className={`w-full flex items-center justify-between py-3 px-4 rounded-md transition-colors text-gray-300 hover:bg-gray-700`}
                    onClick={() => toggleDropdown(item.name)}
                  >
                    <div className="flex items-center">
                      <item.icon className="w-5 h-5" />
                      {!collapsed && (
                        <span className="ml-3">{item.name}</span>
                      )}
                    </div>
                    {!collapsed && (
                      <ChevronDownIcon
                        className={`w-4 h-4 transition-transform duration-200 ${openDropdowns[item.name] ? 'transform rotate-180' : ''}`}
                      />
                    )}
                  </button>

                  {(openDropdowns[item.name] || collapsed) && (
                    <ul className={`mt-1 ${collapsed ? 'absolute left-16 top-auto bg-gray-800 rounded-md shadow-lg w-48' : 'ml-6'}`}>
                      {item.children.map((child) => (
                        <li key={child.name}>
                          <Link
                            href={child.path}
                            className={`flex items-center py-2 px-4 rounded-md transition-colors text-gray-300 hover:bg-gray-700`}
                            onClick={() => setActiveItem(child.name)}
                          >
                            <child.icon className="w-5 h-5" />
                            <span className="ml-3">{child.name}</span>
                          </Link>
                        </li>
                      ))}
                    </ul>
                  )}
                </div>
              ) : (
                <Link
                  href={item.path}
                  className={`flex items-center py-3 px-4 rounded-md transition-colors ${activeItem === item.name
                    ? 'bg-orange-400 text-white'
                    : 'text-gray-300 hover:bg-gray-700'
                    }`}
                  onClick={() => setActiveItem(item.name)}
                >
                  <item.icon className="w-5 h-5" />
                  {!collapsed && (
                    <span className="ml-3">{item.name}</span>
                  )}
                </Link>
              )}
            </li>
          ))}
        </ul>
      </nav>

      {/* Logout */}
      <div className="absolute bottom-0 w-full p-4 border-t border-gray-700">
        <Logout
          className="w-full text-red-400 hover:text-red-300 hover:bg-red-900/50 rounded-md transition-colors font-semibold"
          iconOnly={collapsed}
        />
      </div>
    </aside>
  );
}
