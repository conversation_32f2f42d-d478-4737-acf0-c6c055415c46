"use client";

import contentUpload from '@/app/utils/contentUpload';
import { usePathname } from 'next/navigation';
import { useState, useEffect } from 'react';
import React from 'react';
import { AiOutlineCloudUpload } from 'react-icons/ai';
import { FaImage } from 'react-icons/fa';
import { FiPlus, FiMinus, FiDollarSign, FiPackage, FiImage } from 'react-icons/fi';
import Swal from 'sweetalert2';
import Image from 'next/image';
import toast from 'react-hot-toast';

const partnerProdcut = {
    name: '',
    sku: '',
    description: '',
    brand_id: '',
    category_id: '',
    currency_code: 'AUD', // AUD, NZD, USD
    number_of_products: 1,
    per_pack_price: '',
    per_pack_special_price: '',
    special_price_title: '',
    add_bulk_prices: false,
    bulk_prices: [],
    min_order_packs: '',
    min_order_value: '',
    free_shipping_threshold: '', // packs
    packs_per_pallet: '',
    meta_title: '',
    meta_description: '',
    meta_keywords: '',
    quantity: 0,
    images: [],
    default_customer_margin: 15,
    default_partner_margin: 10,
    default_customer_margin_type: 'percentage',
    default_partner_margin_type: 'percentage',
};

const superAdminProdcut = {
    ...partnerProdcut,
    customer_margin: 15,
    partner_margin: 10,
    customer_margin_type: 'percentage',
    partner_margin_type: 'percentage',
};


export default function ProductForm({ isAddMode, isUpdateMode }) {
    const pathname = usePathname();
    const [, adminType, , , id] = pathname.split('/');

    const [product, setProduct] = useState(adminType === "super-admin" ? superAdminProdcut : partnerProdcut);
    const [fetchProductData, setFetchProductData] = useState({});
    const [errors, setErrors] = useState({});
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [categoriesOptions, setCategoriesOptions] = useState([]);
    const [brandsOptions, setBrandsOptions] = useState([]);

    // Fetch product data if in update mode
    useEffect(() => {
        getCategories();
        getBrands();

        if (isUpdateMode && id) {
            fetchProduct(id);
        }
    }, [isUpdateMode, id]);

    async function getCategories() {
        try {
            let allCategories = [];
            let currentPage = 1;
            let hasMorePages = true;

            // Fetch all pages of categories
            while (hasMorePages) {
                const response = await fetch(`https://b2b.instinctfusionx.xyz/public/api/v1/categories?page=${currentPage}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        // 'Authorization': `Bearer ${adminType === "super-admin" && localStorage.getItem('superAdminAuthToken') || adminType === "partner-admin" && localStorage.getItem('partnerAuthToken')}`
                    }
                });

                const data = await response.json();
                // console.log(`Page ${currentPage}:`, data);

                if (data.success === true && data.data.categories.data.length > 0) {
                    // Add categories from current page to the collection
                    allCategories = [...allCategories, ...data.data.categories.data];

                    // Check if there are more pages
                    const pagination = data.data.categories;
                    hasMorePages = currentPage < pagination.last_page;
                    currentPage++;
                } else {
                    // If no data or unsuccessful response, stop pagination
                    hasMorePages = false;
                }
            }

            // Process all collected categories
            if (allCategories.length > 0) {
                const categories = allCategories.map((category, index) => {
                    return {
                        name: category.name,
                        value: category.id,
                    };
                });

                // console.log(`Total categories fetched: ${categories.length}, ${JSON.stringify(categories)}`);
                setCategoriesOptions([...categories]);
            }

            // console.log('All categories:', categoriesOptions);

        } catch (error) {
            console.error('Error fetching categories:', error);
        }
    }

    async function getBrands() {
        try {
            let allBrands = [];
            let currentPage = 1;
            let hasMorePages = true;

            // Fetch all pages of brands
            while (hasMorePages) {
                const response = await fetch(`https://b2b.instinctfusionx.xyz/public/api/v1/brands?page=${currentPage}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        // 'Authorization': `Bearer ${adminType === "super-admin" && localStorage.getItem('superAdminAuthToken') || adminType === "partner-admin" && localStorage.getItem('partnerAuthToken')}`
                    }
                });

                const data = await response.json();
                // console.log(`Brands Page ${currentPage}:`, data);

                if (data.success === true && data.data.brands.data.length > 0) {
                    // Add brands from current page to the collection
                    allBrands = [...allBrands, ...data.data.brands.data];

                    // Check if there are more pages
                    const pagination = data.data.brands;
                    hasMorePages = currentPage < pagination.last_page;
                    currentPage++;
                } else {
                    // If no data or unsuccessful response, stop pagination
                    hasMorePages = false;
                }
            }

            // Process all collected brands
            if (allBrands.length > 0) {
                const brands = allBrands.map((brand, index) => {
                    return {
                        name: brand.name,
                        value: brand.id,
                    };
                });

                // console.log(`Total brands fetched: ${brands.length}`);
                setBrandsOptions([...brands]);
            }

            // console.log('All brands:', brandsOptions);

        } catch (error) {
            console.error('Error fetching brands:', error);
        }
    }

    const fetchProduct = async (productId) => {
        try {
            // Implement API call to fetch product data
            const url = adminType === "super-admin" && `https://b2b.instinctfusionx.xyz/public/api/v1/admin/products/${productId}` ||
                adminType === "partner-admin" && `https://b2b.instinctfusionx.xyz/public/api/v1/vendor/products/${productId}`
            // console.log(url);

            const token = adminType === "super-admin" && localStorage.getItem('superAdminAuthToken') ||
                adminType === "partner-admin" && localStorage.getItem('partnerAuthToken');
            // console.log(adminType, token);

            if (!token) {
                console.error('No authentication token found.');
                return;
            }
            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                }
            });

            const data = await response.json();
            console.log(data);

            if (data.success === true && data.data.product.id) {
                setFetchProductData(data.data.product);

                const initialState = adminType === "super-admin" ? superAdminProdcut : partnerProdcut;
                const mergedProduct = {
                    ...initialState,
                    ...data.data.product,
                    // Ensure these fields are always defined with fallbacks
                    name: data.data.product.name || '',
                    sku: data.data.product.sku || '',
                    description: data.data.product.description || '',
                    brand_id: data.data.product.brand_id || '',
                    category_id: data.data.product.category_id || '',
                    currency_code: data.data.product.currency.code || 'AUD',
                    number_of_products: data.data.product.pack_price.number_of_products || 1,
                    per_pack_price: data.data.product.pack_price.per_pack_price || '',
                    per_pack_special_price: data.data.product.pack_price.per_pack_special_price || '',
                    special_price_title: data.data.product.pack_price.special_price_title || '',
                    add_bulk_prices: Boolean(data.data.product.add_bulk_prices),
                    bulk_prices: Array.isArray(data.data.product.bulk_prices) ? data.data.product.bulk_prices : [],
                    min_order_packs: data.data.product.min_order_quantity || '',
                    min_order_value: data.data.product.min_order_value || '',
                    free_shipping_threshold: data.data.product.free_shipping_threshold || '',
                    packs_per_pallet: data.data.product.packs_per_pallet || '',
                    meta_title: data.data.product.meta_title || '',
                    meta_description: data.data.product.meta_description || '',
                    meta_keywords: data.data.product.meta_keywords || '',
                    quantity: data.data.product.quantity || 0,
                    images: Array.isArray(data.data.product.images) ? data.data.product.images : []
                };

                // Add super admin specific fields if needed
                if (adminType === "super-admin") {
                    mergedProduct.customer_margin = data.data.product.customer_margin ?? 15;
                    mergedProduct.partner_margin = data.data.product.partner_margin ?? 10;
                    mergedProduct.customer_margin_type = data.data.product.customer_margin_type || 'percentage';
                    mergedProduct.partner_margin_type = data.data.product.partner_margin_type || 'percentage';
                    mergedProduct.default_customer_margin = data.data.product.default_customer_margin ?? 15;
                    mergedProduct.default_partner_margin = data.data.product.default_partner_margin ?? 10;
                    mergedProduct.default_customer_margin_type = data.data.product.default_customer_margin_type || 'percentage';
                    mergedProduct.default_partner_margin_type = data.data.product.default_partner_margin_type || 'percentage';
                }
                // console.log(mergedProduct);

                setProduct(mergedProduct);
            }
        } catch (error) {
            console.error("Error fetching product:", error);
        }
    };

    function handleInputChange(event) {
        const { name, value, type, checked, files } = event.target;

        // Ensure we never set undefined values
        const newValue = type === 'checkbox' ? checked : (value === undefined ? '' : value);

        // Handle category selection
        if (name === 'category_id') {
            const selectedCategory = categoriesOptions.find(category => category.value === value);
            setProduct(prevProduct => ({
                ...prevProduct,
                [name]: selectedCategory ? selectedCategory.value : value // Store ID, not name
            }));
            return; // Add return to prevent double update
        }

        // Handle file inputs
        if (type === 'file') {
            if (name === 'inputImages') {
                setProduct(prevProduct => ({
                    ...prevProduct,
                    [name]: files
                }));
            }
            return; // Early return after handling file input
        }

        // Handle all other inputs
        setProduct(prevProduct => ({
            ...prevProduct,
            [name]: newValue
        }));

        // Clear error for this field when user starts typing
        if (errors[name]) {
            setErrors({
                ...errors,
                [name]: undefined
            });
        }
    }

    // Handle bulk pricing
    const addBulkPrice = () => {
        // Check if we've reached the maximum number of bulk prices
        if (product.bulk_prices.length >= 5) {
            Swal.fire({
                icon: 'warning',
                title: 'Maximum Limit Reached',
                text: 'You can add up to 5 bulk pricing tiers.',
                confirmButtonColor: '#f97316',
            });
            return;
        }

        const lastPrice = product.bulk_prices[product.bulk_prices.length - 1];
        const newNumberOfPacks = lastPrice ? lastPrice.number_of_packs + 5 : 5;

        const newBulkPrice = {
            number_of_packs: newNumberOfPacks,
            per_pack_price: '',
            per_pack_special_price: '',
            special_price_title: '',
        };

        // Add margin fields only for super admin
        if (adminType === "super-admin") {
            newBulkPrice.customer_margin = 15;
            newBulkPrice.partner_margin = 10;
            newBulkPrice.customer_margin_type = 'percentage';
            newBulkPrice.partner_margin_type = 'percentage';
        }

        setProduct({
            ...product,
            bulk_prices: [
                ...product.bulk_prices,
                newBulkPrice
            ]
        });
    };

    const removeBulkPrice = (index) => {
        const updatedPrices = [...product.bulk_prices];
        updatedPrices.splice(index, 1);

        setProduct({
            ...product,
            bulk_prices: updatedPrices
        });
    };

    const handleBulkPriceChange = (index, field, value) => {
        const updatedPrices = [...product.bulk_prices];

        // Handle different field types appropriately and ensure no undefined values
        if (field === 'number_of_packs') {
            updatedPrices[index][field] = parseInt(value, 10) || 0;
        } else if (['per_pack_price', 'per_pack_special_price', 'customer_margin', 'partner_margin'].includes(field)) {
            updatedPrices[index][field] = parseFloat(value) || 0;
        } else {
            // For select fields like margin_type, ensure string values
            updatedPrices[index][field] = value || '';
        }

        setProduct({
            ...product,
            bulk_prices: updatedPrices
        });
    };

    function validateForm() {
        const newErrors = {};

        // Basic required field validation
        if (!product.name.trim()) {
            newErrors.name = 'Product name is required';
        }
        if (!product.sku.trim()) {
            newErrors.sku = 'SKU is required';
        }
        if (!product.per_pack_price || product.per_pack_price <= 0) {
            newErrors.per_pack_price = 'Valid price is required';
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0; // Returns true if no errors
    }

    function handleSanitizeForm(formData) {
        // console.log("handle sanitize form");

        const partnerData = {
            // id: , // unique required 
            name: formData.name, // unique required
            sku: formData.sku, // unique required
            description: formData.description || 'Product Description.', // required
            brand_id: parseInt(formData.brand_id) || 1, // required
            category_id: parseInt(formData.category_id) || 1, // required
            currency_code: 'AUD', // AUD, NZD, USD
            number_of_products: parseInt(formData.number_of_products) || 1,
            per_pack_price: parseFloat(formData.per_pack_price),
            per_pack_special_price: parseFloat(formData.per_pack_special_price),
            special_price_title: formData.special_price_title || '',
            add_bulk_prices: Boolean(formData.add_bulk_prices) || false,
            bulk_prices: formData.bulk_prices || [],
            min_order_packs: parseInt(formData.min_order_packs) || 1,
            min_order_value: parseFloat(formData.min_order_value),
            free_shipping_threshold: parseFloat(formData.free_shipping_threshold), // packs
            packs_per_pallet: parseInt(formData.packs_per_pallet),
            meta_title: formData.meta_title || '',
            meta_description: formData.meta_description || '',
            // meta_keywords: formData.meta_keywords || '',
            // meta_keywords: formData.meta_keywords.split(',').map(item => item.trim()).filter(item => item && isNaN(item) && !/^\s*$/.test(item)).join(',') || [],
            meta_keywords: formData.meta_keywords.split(",").map(item => item.trim()).filter(item => item && isNaN(item) && !/^\s*$/.test(item)).join(",") || [],
            quantity: parseInt(formData.quantity) || 0,
            images: [],
            default_customer_margin: 15,
            default_partner_margin: 10,
            default_customer_margin_type: 'percentage',
            default_partner_margin_type: 'percentage',
        };

        const superAdminData = {
            customer_margin: parseFloat(formData.customer_margin) || partnerData.default_customer_margin,
            partner_margin: parseFloat(formData.partner_margin) || partnerData.default_partner_margin,
            customer_margin_type: 'percentage',
            partner_margin_type: 'percentage',
        };

        const sanitizedProduct = {
            ...partnerData,
            ...(adminType === "super-admin" && superAdminData),
            // Add any additional sanitization logic here
        };
        // console.log(sanitizedProduct);
        // console.log(sanitizedProduct);

        return sanitizedProduct;
    }

    async function handleSuperAdminAddProduct(formData) {
        // console.log(`Super Admin Add Product`);
        // console.log(formData);



        const sanitizedFormData = handleSanitizeForm(formData);
        // console.log(sanitizedFormData);

        const imageResponse = await contentUpload.MultipleImageUpload(formData.inputImages, "superAdminAuthToken");
        // console.log(imageResponse);
        if (imageResponse && imageResponse.length > 0) {
            sanitizedFormData.images = imageResponse;
            // console.log(sanitizedFormData.images);


            // console.log(sanitizedFormData);
            const response = await fetch(`https://b2b.instinctfusionx.xyz/public/api/v1/admin/products`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('superAdminAuthToken')}`
                },
                body: JSON.stringify(sanitizedFormData) // Make sure to send the form data
            });
            const data = await response.json();
            // console.log(data);

            if (data.success === true && data.data.product.id) {
                // console.log("Product Added");
                Swal.fire({
                    icon: 'success',
                    title: 'Success',
                    text: data.message || 'Product added successfully',
                    showConfirmButton: true,
                    timer: 5000
                });
            } else if (data.success === false && data.errors) {
                // console.log("Product Not Added");
                // console.log(data.message);

                // Format validation errors for display
                let errorMessage = '<ul style="text-align: left; list-style-type: none; padding-left: 0;">';
                for (const field in data.errors) {
                    errorMessage += `<li><strong>${field.replace('_', ' ')}:</strong> ${data.errors[field].join(', ')}</li>`;
                }
                errorMessage += '</ul>';

                Swal.fire({
                    icon: 'error',
                    title: 'Validation Error',
                    html: errorMessage,
                    confirmButtonColor: '#f97316',
                });
            } else {
                // console.log("Something went wrong");
                Swal.fire({
                    icon: 'error',
                    title: 'Something went wrong',
                    text: data.message || 'Please try again later',
                    showConfirmButton: true,
                });
            }
        }

        setIsSubmitting(false);
    }

    // task!!! api needed.
    async function handleSuperAdminUpdateProduct(formData) {
        console.log(`Super Admin Update Product`);
        setIsSubmitting(true);

        try {
            // Get sanitized form data without images
            const sanitizedFormData = handleSanitizeForm(formData);

            // Only include images if they are different from the original
            console.log(formData);

            if (!formData?.inputImages || formData?.inputImages.length > 0) {
                const imageResponse = await contentUpload.MultipleImageUpload(formData.inputImages, "superAdminAuthToken");
                console.log(imageResponse);
                if (imageResponse && imageResponse.length > 0) {
                    const updatedImages = [...imageResponse, ...sanitizedFormData.images];
                    if (updatedImages.length > 5) {
                        sanitizedFormData.images = updatedImages.slice(0, 5);
                    } else {
                        sanitizedFormData.images = updatedImages;
                    }
                }

            }

            // console.log('Update data:', sanitizedFormData);

            // task!!! not working. need to review.
            const payload = {
                attributes: Object.keys(sanitizedFormData) || [],
                values: Object.values(sanitizedFormData) || []
            }
            console.log(payload);
            console.log(JSON.stringify(payload));


            // Make API call here when ready
            const response = await fetch(`https://b2b.instinctfusionx.xyz/public/api/v1/admin/products/${id}/update-attributes`, {
                method: 'PATCH',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('superAdminAuthToken')}`
                },
                body: JSON.stringify(payload)
            });
            const data = await response.json();
            console.log(data);

            if (data.success === true) {
                toast.success(data.message || 'Product updated successfully!');

                Swal.fire({
                    icon: 'success',
                    title: 'Success',
                    text: data.message || 'Product updated successfully',
                    showConfirmButton: true,
                    timer: 3000
                });
            } else if (data.success === false && data.errors) {
                // Format validation errors for display
                let errorMessage = '<ul style="text-align: left; list-style-type: none; padding-left: 0;">';
                for (const field in data.errors) {
                    errorMessage += `<li><strong>${field.replace('_', ' ')}:</strong> ${data.errors[field].join(', ')}</li>`;
                }
                errorMessage += '</ul>';

                Swal.fire({
                    icon: 'error',
                    title: 'Validation Error',
                    html: errorMessage,
                    confirmButtonColor: '#f97316',
                    showConfirmButton: true,
                });
            }
        } catch (error) {
            console.error('Error updating product:', error);
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'Failed to update product'
            });
        } finally {
            setIsSubmitting(false);
        }

    }

    async function handlePartnerAdminAddProduct(formData) {
        console.log(`Partner Admin Add Product`);
        console.log(formData);

        const sanitizedFormData = handleSanitizeForm(formData);
        console.log(sanitizedFormData);

        const imageResponse = await contentUpload.MultipleImageUpload(formData.inputImages, "partnerAuthToken");
        console.log(imageResponse);

        if (imageResponse && imageResponse.length > 0) {
            sanitizedFormData.images = imageResponse;
            console.log(sanitizedFormData);

            const response = await fetch(`https://b2b.instinctfusionx.xyz/public/api/v1/vendor/products`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('partnerAuthToken')}`
                },
                body: JSON.stringify(sanitizedFormData) // Make sure to send the form data
            });
            const data = await response.json();
            console.log(data);

            if (data.success === true && data.data.product.id) {
                console.log("Product Added");
                Swal.fire({
                    icon: 'success',
                    title: 'Success',
                    text: data.message || 'Product added successfully',
                    showConfirmButton: true,
                    timer: 3000
                });
            } else if (data.success === false && data.errors) {
                console.log("Product Not Added");
                console.log(data.message);

                // Format validation errors for display
                let errorMessage = '<ul style="text-align: left; list-style-type: none; padding-left: 0;">';
                for (const field in data.errors) {
                    errorMessage += `<li><strong>${field.replace('_', ' ')}:</strong> ${data.errors[field].join(', ')}</li>`;
                }
                errorMessage += '</ul>';

                Swal.fire({
                    icon: 'error',
                    title: 'Validation Error',
                    html: errorMessage,
                    confirmButtonColor: '#f97316',
                    showConfirmButton: true,
                });
            } else {
                console.log("Something went wrong");
                Swal.fire({
                    icon: 'error',
                    title: 'Something went wrong',
                    text: data.message || 'Please try again later',
                    showConfirmButton: true,
                });
            }
        }

        setIsSubmitting(false);
    }

    // task!!! api needed.
    async function handlePartnerAdminUpdateProduct(formData) {
        console.log(`Partner Admin Update Product`);
        // api is not ready yet

        try {
            // Get sanitized form data without images
            const sanitizedFormData = handleSanitizeForm(formData);

            // Only include images if they are different from the original
            if (formData.inputImages.length > 0) {
                const imageResponse = await contentUpload.MultipleImageUpload(formData.inputImages, "superAdminAuthToken");
                console.log(imageResponse);
                if (imageResponse && imageResponse.length > 0) {
                    const updatedImages = [...imageResponse, ...sanitizedFormData.images];
                    if (updatedImages.length > 5) {
                        sanitizedFormData.images = updatedImages.slice(0, 5);
                    } else {
                        sanitizedFormData.images = updatedImages;
                    }
                }

                console.log('Update data:', updateData);

                // Make API call here when ready
                // const response = await fetch(`${API_URL}/products/${id}`, {
                //     method: 'PUT',
                //     headers: {
                //         'Content-Type': 'application/json',
                //         'Authorization': `Bearer ${localStorage.getItem('partnerAuthToken')}`
                //     },
                //     body: JSON.stringify(updateData)
                // });

                Swal.fire({
                    icon: 'success',
                    title: 'Success',
                    text: 'Product updated successfully'
                });
            }
        } catch (error) {
            console.error('Error updating product:', error);
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'Failed to update product'
            });
        } finally {
            setIsSubmitting(false);
        }
    }

    async function handleFormSubmit(event) {
        event.preventDefault();

        // Ensure add_bulk_prices is true if there are bulk prices
        if (product.bulk_prices.length > 0 && !product.add_bulk_prices) {
            setProduct(prev => ({
                ...prev,
                add_bulk_prices: true
            }));
        }

        // Validate form before submission
        if (!validateForm()) {
            Swal.fire({
                icon: 'error',
                title: 'Validation Error',
                text: 'Please check all required fields',
            });
            return;
        }

        setIsSubmitting(true);
        // console.log("PRODUCT: ", product);

        isAddMode && adminType === "super-admin" && handleSuperAdminAddProduct(product);
        isAddMode && adminType === "partner-admin" && handlePartnerAdminAddProduct(product);

        isUpdateMode && adminType === "super-admin" && handleSuperAdminUpdateProduct(product);
        isUpdateMode && adminType === "partner-admin" && handlePartnerAdminUpdateProduct(product);
    }

    function handleDeleteProduct() {
        Swal.fire({
            title: 'Are you sure?',
            text: "You won't be able to revert this!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Yes, delete it!'
        }).then(async (result) => {
            if (result.isConfirmed) {
                try {
                    // Implement API call to delete product
                    // const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/products/${id}`, {
                    //     method: 'DELETE',
                    //     headers: {
                    //         'Content-Type': 'application/json',
                    //         'Authorization': `Bearer ${localStorage.getItem('authToken')}`
                    //     }
                    // });

                    // const data = await response.json();

                    // if (data.success) {
                    //     Swal.fire(
                    //         'Deleted!',
                    //         'Your product has been deleted.',
                    //         'success'
                    //     );
                    //     // Redirect to products list
                    //     // router.push('/admin/products');
                    // } else {
                    //     Swal.fire(
                    //         'Error!',
                    //         data.message || 'Failed to delete product',
                    //         'error'
                    //     );
                    // }
                } catch (error) {
                    console.error("Error deleting product:", error);
                    Swal.fire(
                        'Error!',
                        'An unexpected error occurred',
                        'error'
                    );
                }
            }
        });
    }

    return (
        <section className='text-gray-700 max-w-4xl mx-auto'>
            <h2 className="text-2xl font-semibold mb-6">
                {isAddMode ? 'Add New Product' : 'Update Product'}
            </h2>

            <form onSubmit={handleFormSubmit} className="space-y-6">
                {/* Basic Information - Common to both admin types */}
                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                    <h3 className="text-xl font-medium mb-4">Basic Information</h3>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label className="block text-sm font-medium mb-1">Product Name</label>
                            <input
                                type="text"
                                name="name"
                                value={product.name}
                                onChange={handleInputChange}
                                className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-orange-400"
                                placeholder="Enter product name"
                            />
                            {errors.name && <p className="text-red-500 text-sm mt-1">{errors.name}</p>}
                        </div>

                        <div>
                            <label className="block text-sm font-medium mb-1">SKU</label>
                            <input
                                type="text"
                                name="sku"
                                value={product.sku}
                                onChange={handleInputChange}
                                className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-orange-400"
                                placeholder="Enter product SKU"
                            />
                            {errors.sku && <p className="text-red-500 text-sm mt-1">{errors.sku}</p>}
                        </div>
                    </div>

                    <div className="mt-4">
                        <label className="block text-sm font-medium mb-1">Description</label>
                        <textarea
                            name="description"
                            value={product.description}
                            onChange={handleInputChange}
                            rows="4"
                            className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-orange-400"
                            placeholder="Enter product description"
                        ></textarea>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                        <div>
                            <label className="block text-sm font-medium mb-1">Brand</label>
                            <select
                                name="brand_id"
                                value={isUpdateMode ? product.brand?.id : product.brand_id}
                                onChange={handleInputChange}
                                className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-orange-400"
                                style={{
                                    maxHeight: '200px',
                                    overflowY: 'auto'
                                }}
                                size="1"
                            >
                                {isAddMode && <option value="">Select Brand</option>}
                                {brandsOptions.map((brand, index) => (
                                    <option key={index} value={brand.value}>{brand.name}</option>
                                ))}
                            </select>
                        </div>

                        <div>
                            <label className="block text-sm font-medium mb-1">Category</label>
                            <select
                                name="category_id"
                                value={isUpdateMode ? (product.categories?.[0]?.id || product.category_id) : product.category_id}
                                onChange={handleInputChange}
                                className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-orange-400"
                                style={{
                                    maxHeight: '200px',
                                    overflowY: 'auto'
                                }}
                                size="1"
                            >
                                {isAddMode && <option value="">Select Category</option>}
                                {categoriesOptions.map((category, index) => (
                                    <option key={index} value={category.value}>
                                        {category.name}
                                    </option>
                                ))}
                            </select>
                        </div>
                    </div>
                </div>

                {/* Pricing Information - Common to both admin types */}
                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                    <h3 className="text-xl font-medium mb-4">Pricing Information</h3>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <label className="block text-sm font-medium mb-1">Currency</label>
                            <select
                                name="currency_code"
                                value={product.currency_code}
                                onChange={handleInputChange}
                                className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-orange-400"
                            >
                                <option value="AUD">AUD</option>
                                {/* <option value="NZD">NZD</option> */}
                                <option value="USD">USD</option>
                            </select>
                        </div>

                        <div>
                            <label className="block text-sm font-medium mb-1">Products Per Pack</label>
                            <input
                                type="number"
                                name="number_of_products"
                                value={product.number_of_products}
                                onChange={handleInputChange}
                                min="1"
                                className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-orange-400"
                            />
                        </div>

                        <div>
                            <label className="block text-sm font-medium mb-1">Price Per Pack</label>
                            <input
                                type="number"
                                name="per_pack_price"
                                value={product.per_pack_price}
                                onChange={handleInputChange}
                                step="0.01"
                                min="0"
                                className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-orange-400"
                                placeholder="0.00"
                            />
                            {errors.per_pack_price && <p className="text-red-500 text-sm mt-1">{errors.per_pack_price}</p>}
                        </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                        <div>
                            <label className="block text-sm font-medium mb-1">Special Price (Optional)</label>
                            <input
                                type="number"
                                name="per_pack_special_price"
                                value={product.per_pack_special_price}
                                onChange={handleInputChange}
                                step="0.01"
                                min="0"
                                className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-orange-400"
                                placeholder="0.00"
                            />
                        </div>

                        <div>
                            <label className="block text-sm font-medium mb-1">Special Price Title</label>
                            <input
                                type="text"
                                name="special_price_title"
                                value={product.special_price_title}
                                onChange={handleInputChange}
                                className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-orange-400"
                                placeholder="e.g., Sale Price, Limited Offer"
                            />
                        </div>
                    </div>

                    {/* Bulk Pricing Section */}
                    <div className="mt-6">
                        <div className="flex items-center mb-4">
                            <input
                                type="checkbox"
                                name="add_bulk_prices"
                                checked={product.add_bulk_prices}
                                onChange={handleInputChange}
                                id="add_bulk_prices"
                                className="mr-2 h-4 w-4 text-orange-500"
                            />
                            <label htmlFor="add_bulk_prices" className="font-medium">Enable Bulk Pricing</label>
                        </div>

                        {/* Show bulk pricing section if checkbox is checked OR if there are existing bulk prices */}
                        {(product.add_bulk_prices || product.bulk_prices.length > 0) && (
                            <div className="bg-gray-50 p-4 rounded-md border border-gray-200">
                                <h4 className="font-medium mb-3 flex items-center">
                                    <FiDollarSign className="mr-1" /> Bulk Pricing Tiers
                                </h4>

                                {product.bulk_prices.length === 0 ? (
                                    <p className="text-gray-500 mb-2">No bulk pricing tiers added yet.</p>
                                ) : (
                                    <div className="space-y-6 mb-3">
                                        {product.bulk_prices.map((price, index) => (
                                            <div key={index} className="p-4 bg-white rounded-md border border-gray-200">
                                                <div className="flex justify-between items-center mb-3">
                                                    <h5 className="font-medium">Tier {index + 1}</h5>
                                                    <button
                                                        type="button"
                                                        onClick={() => removeBulkPrice(index)}
                                                        className="px-3 py-1 bg-red-50 text-red-500 rounded-md hover:bg-red-100 transition-colors flex items-center"
                                                    >
                                                        <FiMinus className="w-4 h-4 mr-1" /> Remove
                                                    </button>
                                                </div>

                                                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                                    <div>
                                                        <label className="block text-sm mb-1">Number of Packs</label>
                                                        <input
                                                            type="number"
                                                            value={price.number_of_packs}
                                                            onChange={(e) => handleBulkPriceChange(index, 'number_of_packs', e.target.value)}
                                                            min="1"
                                                            className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-orange-400"
                                                        />
                                                    </div>

                                                    <div>
                                                        <label className="block text-sm mb-1">Price Per Pack</label>
                                                        <input
                                                            type="number"
                                                            value={price.per_pack_price}
                                                            onChange={(e) => handleBulkPriceChange(index, 'per_pack_price', e.target.value)}
                                                            step="0.01"
                                                            min="0"
                                                            className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-orange-400"
                                                        />
                                                    </div>

                                                    <div>
                                                        <label className="block text-sm mb-1">Special Price (Optional)</label>
                                                        <input
                                                            type="number"
                                                            value={price.per_pack_special_price}
                                                            onChange={(e) => handleBulkPriceChange(index, 'per_pack_special_price', e.target.value)}
                                                            step="0.01"
                                                            min="0"
                                                            className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-orange-400"
                                                        />
                                                    </div>
                                                </div>

                                                {/* Add Special Price Title field */}
                                                <div className="mt-3">
                                                    <label className="block text-sm mb-1">Special Price Title (Optional)</label>
                                                    <input
                                                        type="text"
                                                        value={price.special_price_title || ''}
                                                        onChange={(e) => handleBulkPriceChange(index, 'special_price_title', e.target.value)}
                                                        className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-orange-400"
                                                        placeholder="e.g., Bulk Discount, Volume Offer"
                                                    />
                                                </div>

                                                {/* Super Admin specific margin fields */}
                                                {adminType === "super-admin" && (
                                                    <div className="mt-4 pt-4 border-t border-gray-200">
                                                        <h6 className="font-medium mb-3">Margin Settings</h6>
                                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                            <div className="space-y-4">
                                                                <div className="flex items-end gap-3">
                                                                    <div className="flex-1">
                                                                        <label className="block text-sm mb-1">Customer Margin</label>
                                                                        <input
                                                                            type="number"
                                                                            value={price.customer_margin}
                                                                            onChange={(e) => handleBulkPriceChange(index, 'customer_margin', e.target.value)}
                                                                            step="0.01"
                                                                            min="0"
                                                                            className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-orange-400"
                                                                        />
                                                                    </div>

                                                                    <div>
                                                                        <label className="block text-sm mb-1">Type</label>
                                                                        <select
                                                                            value={price.customer_margin_type}
                                                                            onChange={(e) => handleBulkPriceChange(index, 'customer_margin_type', e.target.value)}
                                                                            className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-orange-400"
                                                                        >
                                                                            <option value="percentage">Percentage (%)</option>
                                                                            <option value="fixed">Fixed Amount</option>
                                                                        </select>
                                                                    </div>
                                                                </div>
                                                            </div>

                                                            <div className="space-y-4">
                                                                <div className="flex items-end gap-3">
                                                                    <div className="flex-1">
                                                                        <label className="block text-sm mb-1">Partner Margin</label>
                                                                        <input
                                                                            type="number"
                                                                            value={price.partner_margin}
                                                                            onChange={(e) => handleBulkPriceChange(index, 'partner_margin', e.target.value)}
                                                                            step="0.01"
                                                                            min="0"
                                                                            className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-orange-400"
                                                                        />
                                                                    </div>

                                                                    <div>
                                                                        <label className="block text-sm mb-1">Type</label>
                                                                        <select
                                                                            value={price.partner_margin_type}
                                                                            onChange={(e) => handleBulkPriceChange(index, 'partner_margin_type', e.target.value)}
                                                                            className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-orange-400"
                                                                        >
                                                                            <option value="percentage">Percentage (%)</option>
                                                                            <option value="fixed">Fixed Amount</option>
                                                                        </select>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                )}
                                            </div>
                                        ))}
                                    </div>
                                )}

                                {/* Only show the Add Price Tier button if less than 5 tiers exist */}
                                {product.bulk_prices.length < 5 ? (
                                    <button
                                        type="button"
                                        onClick={addBulkPrice}
                                        className="flex items-center px-4 py-2 bg-orange-50 text-orange-500 rounded-md hover:bg-orange-100 transition-colors"
                                    >
                                        <FiPlus className="mr-1" /> Add Price Tier
                                    </button>
                                ) : (
                                    <p className="text-orange-500 mt-2 flex items-center">
                                        <FiDollarSign className="mr-1" /> Maximum of 5 bulk pricing tiers allowed
                                    </p>
                                )}
                            </div>
                        )}
                    </div>
                </div>

                {/* Inventory - Common to both admin types */}
                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                    <h3 className="text-xl font-medium mb-4">Stock<sup className='text-xs'>(Inventory)</sup></h3>

                    <div>
                        <label className="block text-sm font-medium mb-1">Quantity</label>
                        <input
                            type="number"
                            name="quantity"
                            value={product.quantity}
                            onChange={handleInputChange}
                            min="0"
                            className="w-full md:w-1/3 px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-orange-400"
                        />
                    </div>
                </div>

                {/* SEO Information - Common to both admin types */}
                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                    <h3 className="text-xl font-medium mb-4">SEO Information</h3>

                    <div className="space-y-4">
                        <div>
                            <label className="block text-sm font-medium mb-1">Meta Title</label>
                            <input
                                type="text"
                                name="meta_title"
                                value={product.meta_title}
                                onChange={handleInputChange}
                                className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-orange-400"
                            />
                        </div>

                        <div>
                            <label className="block text-sm font-medium mb-1">Meta Description</label>
                            <textarea
                                name="meta_description"
                                value={product.meta_description}
                                onChange={handleInputChange}
                                rows="2"
                                className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-orange-400"
                            ></textarea>
                        </div>

                        <div>
                            <label className="block text-sm font-medium mb-1">Meta Keywords</label>
                            <input
                                type="text"
                                name="meta_keywords"
                                value={product.meta_keywords}
                                onChange={handleInputChange}
                                className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-orange-400"
                                placeholder="Comma separated keywords"
                            />
                        </div>
                    </div>
                </div>

                {/* Super Admin Specific Fields */}
                {adminType === "super-admin" && (
                    <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                        <h3 className="text-xl font-medium mb-4">Margin Settings</h3>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div className="space-y-4">
                                <h4 className="font-medium">Customer Margin</h4>

                                <div className="flex items-end gap-3">
                                    <div className="flex-1">
                                        <label className="block text-sm font-medium mb-1">Margin Value</label>
                                        <input
                                            type="number"
                                            name="customer_margin"
                                            value={product.customer_margin}
                                            onChange={handleInputChange}
                                            step="0.01"
                                            min="0"
                                            className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-orange-400"
                                        />
                                    </div>

                                    <div>
                                        <label className="block text-sm font-medium mb-1">Type</label>
                                        <select
                                            name="customer_margin_type"
                                            value={product.customer_margin_type}
                                            onChange={handleInputChange}
                                            className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-orange-400"
                                        >
                                            <option value="percentage">Percentage (%)</option>
                                            <option value="fixed">Fixed Amount</option>
                                        </select>
                                    </div>
                                </div>

                                <div className="flex items-end gap-3">
                                    <div className="flex-1">
                                        <label className="block text-sm font-medium mb-1">Default Margin Value</label>
                                        <input
                                            type="number"
                                            name="default_customer_margin"
                                            value={product.default_customer_margin}
                                            onChange={handleInputChange}
                                            step="0.01"
                                            min="0"
                                            className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-orange-400"
                                        />
                                    </div>

                                    <div>
                                        <label className="block text-sm font-medium mb-1">Default Type</label>
                                        <select
                                            name="default_customer_margin_type"
                                            value={product.default_customer_margin_type}
                                            onChange={handleInputChange}
                                            className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-orange-400"
                                        >
                                            <option value="percentage">Percentage (%)</option>
                                            <option value="fixed">Fixed Amount</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div className="space-y-4">
                                <h4 className="font-medium">Partner Margin</h4>

                                <div className="flex items-end gap-3">
                                    <div className="flex-1">
                                        <label className="block text-sm font-medium mb-1">Margin Value</label>
                                        <input
                                            type="number"
                                            name="partner_margin"
                                            value={product.partner_margin}
                                            onChange={handleInputChange}
                                            step="0.01"
                                            min="0"
                                            className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-orange-400"
                                        />
                                    </div>

                                    <div>
                                        <label className="block text-sm font-medium mb-1">Type</label>
                                        <select
                                            name="partner_margin_type"
                                            value={product.partner_margin_type}
                                            onChange={handleInputChange}
                                            className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-orange-400"
                                        >
                                            <option value="percentage">Percentage (%)</option>
                                            <option value="fixed">Fixed Amount</option>
                                        </select>
                                    </div>
                                </div>

                                <div className="flex items-end gap-3">
                                    <div className="flex-1">
                                        <label className="block text-sm font-medium mb-1">Default Margin Value</label>
                                        <input
                                            type="number"
                                            name="default_partner_margin"
                                            value={product.default_partner_margin}
                                            onChange={handleInputChange}
                                            step="0.01"
                                            min="0"
                                            className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-orange-400"
                                        />
                                    </div>

                                    <div>
                                        <label className="block text-sm font-medium mb-1">Default Type</label>
                                        <select
                                            name="default_partner_margin_type"
                                            value={product.default_partner_margin_type}
                                            onChange={handleInputChange}
                                            className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-orange-400"
                                        >
                                            <option value="percentage">Percentage (%)</option>
                                            <option value="fixed">Fixed Amount</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                )}

                {/* Minimum Order Requirements */}
                <div className="space-y-4 p-6 bg-white rounded-xl shadow-md">
                    <h3 className="text-lg font-semibold text-gray-800">Order Requirements</h3>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {/* Minimum Order Packs */}
                        <div className="relative">
                            <label className="block text-sm font-medium text-gray-700 mb-1" htmlFor="min_order_packs">
                                Minimum Order Packs
                            </label>
                            <div className="relative">
                                <input
                                    type="number"
                                    id="min_order_packs"
                                    name="min_order_packs"
                                    value={product.min_order_packs}
                                    onChange={handleInputChange}
                                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                                    placeholder="Enter minimum packs"
                                    min="0"
                                />
                                <FiPackage className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" />
                            </div>
                            {errors.min_order_packs && (
                                <p className="mt-1 text-sm text-red-600">{errors.min_order_packs}</p>
                            )}
                        </div>

                        {/* Minimum Order Value */}
                        <div className="relative">
                            <label className="block text-sm font-medium text-gray-700 mb-1" htmlFor="min_order_value">
                                Minimum Order Value
                            </label>
                            <div className="relative">
                                <input
                                    type="number"
                                    id="min_order_value"
                                    name="min_order_value"
                                    value={product.min_order_value}
                                    onChange={handleInputChange}
                                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                                    placeholder="Enter minimum value"
                                    min="0"
                                    step="0.01"
                                />
                                <FiDollarSign className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" />
                            </div>
                            {errors.min_order_value && (
                                <p className="mt-1 text-sm text-red-600">{errors.min_order_value}</p>
                            )}
                        </div>

                        {/* Free Shipping Threshold */}
                        <div className="relative">
                            <label className="block text-sm font-medium text-gray-700 mb-1" htmlFor="free_shipping_threshold">
                                Free Shipping Threshold (Packs)
                            </label>
                            <div className="relative">
                                <input
                                    type="number"
                                    id="free_shipping_threshold"
                                    name="free_shipping_threshold"
                                    value={product.free_shipping_threshold}
                                    onChange={handleInputChange}
                                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                                    placeholder="Enter free shipping threshold"
                                    min="0"
                                />
                                <FiPackage className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" />
                            </div>
                            {errors.free_shipping_threshold && (
                                <p className="mt-1 text-sm text-red-600">{errors.free_shipping_threshold}</p>
                            )}
                        </div>

                        {/* Packs Per Pallet */}
                        <div className="relative">
                            <label className="block text-sm font-medium text-gray-700 mb-1" htmlFor="packs_per_pallet">
                                Packs Per Pallet
                            </label>
                            <div className="relative">
                                <input
                                    type="number"
                                    id="packs_per_pallet"
                                    name="packs_per_pallet"
                                    value={product.packs_per_pallet}
                                    onChange={handleInputChange}
                                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                                    placeholder="Enter packs per pallet"
                                    min="0"
                                />
                                <FiPackage className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" />
                            </div>
                            {errors.packs_per_pallet && (
                                <p className="mt-1 text-sm text-red-600">{errors.packs_per_pallet}</p>
                            )}
                        </div>
                    </div>
                </div>

                {/* Image */}
                <div className='w-full bg-white p-2'>
                    <label className="text-sm font-medium text-gray-700 flex items-center gap-2">
                        <FaImage className="text-emerald-500 text-2xl" />
                        Image Upload
                    </label>
                    <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-lg hover:border-orange-400 transition-all duration-300 -hover:shadow-md">
                        <div className="space-y-2 text-center">
                            <AiOutlineCloudUpload className="mx-auto h-12 w-12 text-purple-400 -hover:text-orange-500 transition-colors duration-300" />
                            <div className="flex text-sm text-gray-600">
                                <label className="relative cursor-pointer bg-white rounded-md font-medium text-orange-600 hover:text-orange-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-orange-500">
                                    <span>Upload Image (up to 5MB JPG, PNG, )</span>
                                    <input
                                        type="file"
                                        name="inputImages"
                                        accept="image/*"
                                        onChange={handleInputChange}
                                        className="sr-only"
                                        multiple
                                    />
                                </label>
                            </div>
                            <p className="text-xs text-gray-500">PNG, JPG, GIF and webp up to 5MB</p>

                        </div>
                    </div>

                    {isUpdateMode && (
                        <div className="mt-4">
                            <h3 className="text-lg font-medium mb-2">Product Images</h3>
                            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                                {product.images && product.images.length > 0 && product.images.map((image, index) => (
                                    <div key={index} className="relative border rounded-lg overflow-hidden h-40">
                                        {image.url && (
                                            <Image
                                                src={image.url || '/images/placeholder-product.jpg'}
                                                alt={image.alt || `${product.name} product image ${index + 1}` || `Product image ${index + 1}`}
                                                className="w-full h-full object-cover"
                                                width={200}
                                                height={200}
                                            />
                                        )}
                                    </div>
                                ))}
                            </div>
                        </div>
                    )}
                </div>

                {/* Form Actions */}
                <div className="flex justify-between items-center my-5">
                    {/* <div>
                        {isUpdateMode && (
                            <button
                                type="button"
                                onClick={handleDeleteProduct}
                                className="px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 transition-colors"
                                disabled={isSubmitting}
                            >
                                Delete Product
                            </button>
                        )}
                    </div> */}

                    <div className="flex gap-3">
                        {/* <button
                            type="button"
                            className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors"
                        >
                            Cancel
                        </button> */}

                        {/* <button
                            type="submit"
                            className="px-6 py-2 bg-orange-500 text-white rounded-md hover:bg-orange-600 transition-colors"
                            disabled={isSubmitting}
                        >
                            {isSubmitting ? 'Saving...' : isAddMode ? 'Add Product' : 'Update Product'}
                        </button> */}

                        {isAddMode && adminType === "super-admin" && <button type='submit'
                            className="px-4 py-2 bg-green-400 text-white rounded-md hover:bg-orange-400 transition-colors"
                        >suepr-admin | Add</button>}

                        {isUpdateMode && adminType === "super-admin" && <button type='submit'
                            className="px-4 py-2 bg-green-400 text-white rounded-md hover:bg-orange-400 transition-colors"
                        >super-admin | update</button>}

                        {isAddMode && adminType === "partner-admin" && <button type='submit'
                            className="px-4 py-2 bg-green-400 text-white rounded-md hover:bg-orange-400 transition-colors"
                        >partner-admin | Add</button>}

                        {isUpdateMode && adminType === "partner-admin" && <button type='submit'
                            className="px-4 py-2 bg-green-400 text-white rounded-md hover:bg-orange-400 transition-colors"
                        >partner-admin | update</button>}
                    </div>
                </div>
            </form>
        </section >
    );
}
