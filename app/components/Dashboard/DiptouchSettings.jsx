
'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { FaDatabase, FaDownload, FaTrash, FaUpload } from 'react-icons/fa6';
import { FaShieldAlt, FaSync } from 'react-icons/fa';
import toast from 'react-hot-toast';
import Swal from 'sweetalert2';

async function handleConfirm({ title = "Confirm Action", text = "This action cannot be undone!" } = {}) {
    const result = await Swal.fire({
        title: title,
        text: text + " Please type CONFIRM to proceed.",
        input: "text",
        inputPlaceholder: "Type CONFIRM here",
        inputAttributes: {
            autocapitalize: "off"
        },
        showCancelButton: true,
        confirmButtonText: "Proceed",
        cancelButtonText: "Cancel",
        showLoaderOnConfirm: true,
        preConfirm: (inputValue) => {
            if (inputValue !== "CONFIRM") {
                Swal.showValidationMessage('Please type "CONFIRM" exactly (case-sensitive)');
                return false;
            }
            return true;
        },
        allowOutsideClick: () => !Swal.isLoading()
    });

    if (result.isConfirmed && result.value) {
        return true;
    }
    return null;
}

const actions = [
    {
        title: 'Clear Trash',
        description: 'Empty trash bin',
        icon: FaTrash,
        color: 'bg-red-500',
        action: () => toast('Clear Trash is coming soon!')
    },
    {
        title: 'Export Data',
        description: 'Download backup data',
        icon: FaDownload,
        color: 'bg-green-500',
        action: () => toast('Export Data is coming soon!')
    },
    {
        title: 'Import Data',
        description: 'Upload backup data',
        icon: FaUpload,
        color: 'bg-purple-500',
        action: () => toast('Import Data is coming soon!')
    },
    {
        title: 'Optimize Database',
        description: 'Improve database performance',
        icon: FaDatabase,
        color: 'bg-green-500',
        action: () => toast('Optimize Database is coming soon!')
    },
    {
        title: 'Reset Cache',
        description: 'Clear application cache',
        icon: FaSync,
        color: 'bg-blue-500',
        action: async () => {
            const confirmed = await handleConfirm({
                title: 'Reset Cache',
                text: 'This will clear all cache data.'
            });
            if (confirmed === true) {
                try {
                    const response = await fetch('https://b2b.instinctfusionx.xyz/public/api/v1/superadmin/system/clear-cache', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${localStorage.getItem('superAdminAuthToken')}`
                        }
                    });

                    if (response.success === true) {
                        toast.success('Cache cleared successfully!');
                        return true;
                    }

                } catch (error) {
                    console.log('error', error);
                    toast.error('Failed to clear cache');
                    return false;
                }

            }
            return false;
        }
    },
    {
        title: 'Security Scan',
        description: 'Perform security check',
        icon: FaShieldAlt,
        color: 'bg-blue-500',
        action: () => toast('Security Scan is coming soon!')
    },
]

export default function DiptouchSettings() {
    return (
        <div
            className="min-h-screen p-6"
            style={{
                background: 'linear-gradient(135deg, #f8fafc 0%, #f1f5f9 50%, #e2e8f0 100%)',
            }}
        >
            <div className="max-w-4xl mx-auto">
                {/* Header */}
                <motion.div
                    className="text-center mb-10"
                    initial={{ opacity: 0, y: -20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, ease: "easeOut" }}
                >
                    <h1 className="text-4xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent mb-3">
                        Settings
                    </h1>
                    <p className="text-gray-500 text-lg font-medium">Manage your account preferences and settings</p>
                </motion.div>

                {/* Widget Grid */}
                <motion.div
                    className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.2, duration: 0.7, ease: "easeOut" }}
                >
                    {actions.map((action, index) => (
                        <motion.button
                            key={index}
                            whileHover={{ scale: 1.05, y: -8 }}
                            whileTap={{ scale: 0.95 }}
                            onClick={action.action}
                            className="group relative overflow-hidden rounded-3xl p-6 backdrop-blur-xl bg-white/60 border border-white/30 shadow-2xl hover:shadow-3xl transition-all duration-500"
                            style={{
                                background: 'linear-gradient(135deg, rgba(255,255,255,0.6) 0%, rgba(255,255,255,0.3) 100%)',
                                backdropFilter: 'blur(20px)',
                                boxShadow: '0 8px 32px rgba(0,0,0,0.08), 0 2px 16px rgba(0,0,0,0.04), inset 0 1px 0 rgba(255,255,255,0.3)',
                            }}
                        >
                            {/* Background Gradient */}
                            <div className="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

                            {/* Content */}
                            <div className="relative z-10 flex flex-col items-center text-center space-y-4">
                                <div
                                    className={`p-4 rounded-2xl ${action.color} shadow-lg group-hover:shadow-xl transition-shadow duration-300`}
                                    style={{
                                        boxShadow: '0 4px 20px rgba(0,0,0,0.15), inset 0 1px 0 rgba(255,255,255,0.2)'
                                    }}
                                >
                                    <action.icon className="h-8 w-8 text-white" />
                                </div>

                                <div className="space-y-1">
                                    <h3 className="text-lg font-bold text-gray-900 leading-tight">
                                        {action.title}
                                    </h3>
                                    <p className="text-sm text-gray-500 font-medium leading-tight">
                                        {action.description}
                                    </p>
                                </div>
                            </div>

                            {/* Subtle shine effect */}
                            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -skew-x-12 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-1000" />
                        </motion.button>
                    ))}
                </motion.div>
            </div>
        </div>
    )
}
