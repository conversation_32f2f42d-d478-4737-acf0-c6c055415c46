'use client';

import { useState, useEffect } from 'react';
import { 
    ShieldCheckIcon,
    ExclamationTriangleIcon,
    XCircleIcon,
    CheckCircleIcon,
    ClockIcon,
    DevicePhoneMobileIcon,
    ComputerDesktopIcon,
    GlobeAltIcon,
    MagnifyingGlassIcon
} from '@heroicons/react/24/outline';

export default function SiteAudit() {
    const [auditData, setAuditData] = useState({
        overview: {
            overallScore: 78,
            lastAudit: '2024-01-18',
            totalIssues: 23,
            criticalIssues: 3,
            warningIssues: 12,
            infoIssues: 8
        },
        categories: [
            {
                name: 'Technical SEO',
                score: 85,
                issues: 5,
                status: 'good'
            },
            {
                name: 'On-Page SEO',
                score: 72,
                issues: 8,
                status: 'warning'
            },
            {
                name: 'Performance',
                score: 68,
                issues: 6,
                status: 'warning'
            },
            {
                name: 'Mobile Usability',
                score: 92,
                issues: 2,
                status: 'good'
            },
            {
                name: 'Security',
                score: 95,
                issues: 1,
                status: 'excellent'
            },
            {
                name: 'Accessibility',
                score: 76,
                issues: 4,
                status: 'warning'
            }
        ],
        issues: [
            {
                id: 1,
                category: 'Technical SEO',
                severity: 'critical',
                title: 'Missing XML Sitemap',
                description: 'No XML sitemap found. This makes it harder for search engines to discover and index your pages.',
                affected: 'All pages',
                solution: 'Generate and submit an XML sitemap to search engines.',
                priority: 'high'
            },
            {
                id: 2,
                category: 'On-Page SEO',
                severity: 'critical',
                title: 'Duplicate Meta Descriptions',
                description: '15 pages have duplicate meta descriptions, which can hurt search rankings.',
                affected: '15 pages',
                solution: 'Write unique meta descriptions for each page.',
                priority: 'high'
            },
            {
                id: 3,
                category: 'Performance',
                severity: 'critical',
                title: 'Large Image Files',
                description: 'Several images are over 1MB, significantly slowing page load times.',
                affected: '8 pages',
                solution: 'Compress images and use modern formats like WebP.',
                priority: 'high'
            },
            {
                id: 4,
                category: 'On-Page SEO',
                severity: 'warning',
                title: 'Missing Alt Text',
                description: '23 images are missing alt text, affecting accessibility and SEO.',
                affected: '12 pages',
                solution: 'Add descriptive alt text to all images.',
                priority: 'medium'
            },
            {
                id: 5,
                category: 'Technical SEO',
                severity: 'warning',
                title: 'Broken Internal Links',
                description: '7 internal links are returning 404 errors.',
                affected: '5 pages',
                solution: 'Fix or remove broken internal links.',
                priority: 'medium'
            },
            {
                id: 6,
                category: 'Performance',
                severity: 'warning',
                title: 'Render-Blocking Resources',
                description: 'CSS and JavaScript files are blocking page rendering.',
                affected: 'All pages',
                solution: 'Optimize CSS delivery and defer non-critical JavaScript.',
                priority: 'medium'
            }
        ]
    });

    const [selectedCategory, setSelectedCategory] = useState('all');
    const [selectedSeverity, setSelectedSeverity] = useState('all');

    const severityColors = {
        critical: 'bg-red-100 text-red-800 border-red-200',
        warning: 'bg-yellow-100 text-yellow-800 border-yellow-200',
        info: 'bg-blue-100 text-blue-800 border-blue-200'
    };

    const severityIcons = {
        critical: <XCircleIcon className="h-4 w-4" />,
        warning: <ExclamationTriangleIcon className="h-4 w-4" />,
        info: <CheckCircleIcon className="h-4 w-4" />
    };

    const getScoreColor = (score) => {
        if (score >= 90) return 'text-green-600 bg-green-100';
        if (score >= 70) return 'text-yellow-600 bg-yellow-100';
        return 'text-red-600 bg-red-100';
    };

    const getStatusColor = (status) => {
        switch (status) {
            case 'excellent': return 'bg-green-100 text-green-800';
            case 'good': return 'bg-blue-100 text-blue-800';
            case 'warning': return 'bg-yellow-100 text-yellow-800';
            case 'critical': return 'bg-red-100 text-red-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    };

    const filteredIssues = auditData.issues.filter(issue => {
        const matchesCategory = selectedCategory === 'all' || issue.category === selectedCategory;
        const matchesSeverity = selectedSeverity === 'all' || issue.severity === selectedSeverity;
        return matchesCategory && matchesSeverity;
    });

    const runNewAudit = () => {
        // Simulate running a new audit
        setAuditData(prev => ({
            ...prev,
            overview: {
                ...prev.overview,
                lastAudit: new Date().toISOString().split('T')[0]
            }
        }));
    };

    return (
        <div className="p-6 bg-gray-50 min-h-screen">
            {/* Header */}
            <div className="flex justify-between items-center mb-8">
                <div>
                    <h1 className="text-3xl font-bold text-gray-900">Site Audit</h1>
                    <p className="text-gray-600 mt-2">Comprehensive analysis of your website&apos;s SEO health</p>
                </div>
                <div className="flex items-center space-x-4">
                    <div className="text-sm text-gray-600">
                        Last audit: {auditData.overview.lastAudit}
                    </div>
                    <button 
                        onClick={runNewAudit}
                        className="bg-orange-500 text-white px-4 py-2 rounded-lg hover:bg-orange-600 transition-colors"
                    >
                        Run New Audit
                    </button>
                </div>
            </div>

            {/* Overall Score */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
                <div className="flex items-center justify-between">
                    <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-2">Overall SEO Health Score</h3>
                        <div className="flex items-center space-x-4">
                            <div className="flex items-center">
                                <div className="w-16 h-16 rounded-full border-4 border-gray-200 flex items-center justify-center relative">
                                    <span className="text-2xl font-bold text-gray-900">{auditData.overview.overallScore}</span>
                                    <div 
                                        className="absolute inset-0 rounded-full border-4 border-orange-500"
                                        style={{
                                            clipPath: `polygon(0 0, ${auditData.overview.overallScore}% 0, ${auditData.overview.overallScore}% 100%, 0 100%)`
                                        }}
                                    ></div>
                                </div>
                            </div>
                            <div className="grid grid-cols-3 gap-4 text-sm">
                                <div className="text-center">
                                    <p className="text-red-600 font-semibold">{auditData.overview.criticalIssues}</p>
                                    <p className="text-gray-600">Critical</p>
                                </div>
                                <div className="text-center">
                                    <p className="text-yellow-600 font-semibold">{auditData.overview.warningIssues}</p>
                                    <p className="text-gray-600">Warnings</p>
                                </div>
                                <div className="text-center">
                                    <p className="text-blue-600 font-semibold">{auditData.overview.infoIssues}</p>
                                    <p className="text-gray-600">Info</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div className="text-right">
                        <p className="text-sm text-gray-600">Total Issues Found</p>
                        <p className="text-3xl font-bold text-gray-900">{auditData.overview.totalIssues}</p>
                    </div>
                </div>
            </div>

            {/* Category Scores */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                {auditData.categories.map((category, index) => (
                    <div key={index} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                        <div className="flex items-center justify-between mb-4">
                            <h4 className="font-semibold text-gray-900">{category.name}</h4>
                            <span className={`inline-flex px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(category.status)}`}>
                                {category.status}
                            </span>
                        </div>
                        <div className="flex items-center justify-between">
                            <div className="flex items-center">
                                <span className={`text-2xl font-bold ${category.score >= 80 ? 'text-green-600' : category.score >= 60 ? 'text-yellow-600' : 'text-red-600'}`}>
                                    {category.score}
                                </span>
                                <span className="text-gray-500 ml-1">/100</span>
                            </div>
                            <div className="text-right">
                                <p className="text-sm text-gray-600">{category.issues} issues</p>
                            </div>
                        </div>
                        <div className="mt-3 w-full bg-gray-200 rounded-full h-2">
                            <div 
                                className={`h-2 rounded-full ${category.score >= 80 ? 'bg-green-500' : category.score >= 60 ? 'bg-yellow-500' : 'bg-red-500'}`}
                                style={{ width: `${category.score}%` }}
                            ></div>
                        </div>
                    </div>
                ))}
            </div>

            {/* Issues List */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                <div className="p-6 border-b border-gray-200">
                    <div className="flex justify-between items-center">
                        <h3 className="text-lg font-semibold text-gray-900">Issues & Recommendations</h3>
                        <div className="flex space-x-4">
                            <select
                                value={selectedCategory}
                                onChange={(e) => setSelectedCategory(e.target.value)}
                                className="border border-gray-300 rounded-lg px-3 py-1 text-sm bg-white text-black focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                            >
                                <option value="all">All Categories</option>
                                {auditData.categories.map(category => (
                                    <option key={category.name} value={category.name}>{category.name}</option>
                                ))}
                            </select>
                            <select
                                value={selectedSeverity}
                                onChange={(e) => setSelectedSeverity(e.target.value)}
                                className="border border-gray-300 rounded-lg px-3 py-1 text-sm bg-white text-black focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                            >
                                <option value="all">All Severities</option>
                                <option value="critical">Critical</option>
                                <option value="warning">Warning</option>
                                <option value="info">Info</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div className="divide-y divide-gray-200">
                    {filteredIssues.map((issue) => (
                        <div key={issue.id} className="p-6">
                            <div className="flex items-start justify-between">
                                <div className="flex-1">
                                    <div className="flex items-center space-x-3 mb-2">
                                        <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium border ${severityColors[issue.severity]}`}>
                                            {severityIcons[issue.severity]}
                                            {issue.severity}
                                        </span>
                                        <span className="inline-flex px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                            {issue.category}
                                        </span>
                                        <span className={`inline-flex px-2 py-1 rounded-full text-xs font-medium ${
                                            issue.priority === 'high' ? 'bg-red-100 text-red-800' :
                                            issue.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                                            'bg-green-100 text-green-800'
                                        }`}>
                                            {issue.priority} priority
                                        </span>
                                    </div>
                                    <h4 className="font-semibold text-gray-900 mb-2">{issue.title}</h4>
                                    <p className="text-gray-600 mb-2">{issue.description}</p>
                                    <div className="text-sm text-gray-500 mb-3">
                                        <span className="font-medium">Affected:</span> {issue.affected}
                                    </div>
                                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                                        <p className="text-sm text-blue-800">
                                            <span className="font-medium">Solution:</span> {issue.solution}
                                        </p>
                                    </div>
                                </div>
                                <div className="ml-6 flex space-x-2">
                                    <button className="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                        Fix Issue
                                    </button>
                                    <button className="text-gray-600 hover:text-gray-800 text-sm font-medium">
                                        Ignore
                                    </button>
                                </div>
                            </div>
                        </div>
                    ))}
                </div>
            </div>

            {/* Audit Tips */}
            <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-blue-900 mb-4">Site Audit Best Practices</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm">
                    <div>
                        <h4 className="font-medium text-blue-800 mb-2">Regular Audits</h4>
                        <p className="text-blue-700">Run comprehensive audits monthly to catch issues early and maintain optimal SEO health.</p>
                    </div>
                    <div>
                        <h4 className="font-medium text-blue-800 mb-2">Priority Focus</h4>
                        <p className="text-blue-700">Address critical issues first, as they have the most significant impact on search rankings.</p>
                    </div>
                    <div>
                        <h4 className="font-medium text-blue-800 mb-2">Monitor Progress</h4>
                        <p className="text-blue-700">Track improvements over time and ensure fixed issues don&apos;t reoccur in future audits.</p>
                    </div>
                </div>
            </div>
        </div>
    );
}
