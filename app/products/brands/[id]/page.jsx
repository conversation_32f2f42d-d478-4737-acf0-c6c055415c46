import { Suspense } from 'react';
import { notFound } from 'next/navigation';
import BrandProductsClient from './BrandProductsClient';
import ModularNavbar from '../../../components/Navigation/ModularNavbar';
import Footer from '../../../components/LandingPage/Footer';

async function getBrandById(id) {
}

// export async function generateMetadata({ params }) {
//     try {
//         const { id } = await params;
//         const brand = await getBrandById(id);

//         if (!brand) {
//             return {
//                 title: 'Brand Not Found - Diptouch',
//                 description: 'The requested brand could not be found.',
//             };
//         }

//         return {
//             title: `${brand.name} Products - Diptouch`,
//             description: brand.description || `Browse all products from ${brand.name} on Diptouch marketplace.`,
//             keywords: `${brand.name}, products, wholesale, B2B, marketplace`,
//         };
//     } catch (error) {
//         console.error('🔍 Error generating metadata:', error);
//         return {
//             title: 'Brand Products - Diptouch',
//             description: 'Browse brand products on Diptouch marketplace.',
//         };
//     }
// }

// task!! need to review later

async function BrandProductsPage({ params, searchParams }) {
    try {
        const { id } = await params;
        const brand = await getBrandById(id);


        return <BrandProductsClient brandId={id} brand={brand} />

        if (!brand) {
            notFound();
        }

        return (
            <div className="min-h-screen bg-white">
                <ModularNavbar />
                <div className="min-h-screen bg-gray-50">
                <div className="container mx-auto px-4 py-8">
                    {/* Brand Header */}
                    <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
                        <div className="flex items-center gap-6">
                            {brand.logo ? (
                                <img
                                    src={brand.logo}
                                    alt={brand.name}
                                    className="w-20 h-20 object-contain rounded-lg border"
                                />
                            ) : (
                                <div className="w-20 h-20 bg-gray-200 rounded-lg flex items-center justify-center text-gray-500 font-semibold text-lg">
                                    {brand.name?.charAt(0)?.toUpperCase() || 'B'}
                                </div>
                            )}
                            <div className="flex-1">
                                <h1 className="text-3xl font-bold text-gray-900 mb-2">
                                    {brand.name}
                                </h1>
                                {brand.description && (
                                    <p className="text-gray-600 mb-3">
                                        {brand.description}
                                    </p>
                                )}
                                <div className="flex items-center gap-4 text-sm text-gray-500">
                                    {brand.website && (
                                        <a
                                            href={brand.website}
                                            target="_blank"
                                            rel="noopener noreferrer"
                                            className="text-orange-600 hover:text-orange-700 underline"
                                        >
                                            Visit Website
                                        </a>
                                    )}
                                    {brand.product_count && (
                                        <span>
                                            {brand.product_count} Products Available
                                        </span>
                                    )}
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Products Section */}
                    <Suspense fallback={
                        <div className="bg-white rounded-lg shadow-sm p-8">
                            <div className="animate-pulse">
                                <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
                                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                                    {[...Array(8)].map((_, i) => (
                                        <div key={i} className="border rounded-lg p-4">
                                            <div className="h-48 bg-gray-200 rounded mb-4"></div>
                                            <div className="h-4 bg-gray-200 rounded mb-2"></div>
                                            <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                                            <div className="h-6 bg-gray-200 rounded w-1/2"></div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        </div>
                    }>
                        <BrandProductsClient
                            brandId={id}
                            brand={brand}
                            searchParams={searchParams}
                        />
                    </Suspense>
                </div>
                </div>
                <Footer />
            </div>
        );
    } catch (error) {
        console.error('🔍 Error in BrandProductsPage:', error);
        return (
            <div className="min-h-screen bg-white">
                <ModularNavbar />
                <div className="min-h-screen bg-gray-50 flex items-center justify-center">
                    <div className="text-center">
                        <h1 className="text-2xl font-bold text-gray-900 mb-4">
                            Something went wrong
                        </h1>
                        <p className="text-gray-600 mb-6">
                            We encountered an error while loading the brand products.
                        </p>
                        <a
                            href="/products/brands"
                            className="bg-orange-600 text-white px-6 py-2 rounded-lg hover:bg-orange-700 transition-colors"
                        >
                            Back to Brands
                        </a>
                    </div>
                </div>
                <Footer />
            </div>
        );
    }
}

export default BrandProductsPage;