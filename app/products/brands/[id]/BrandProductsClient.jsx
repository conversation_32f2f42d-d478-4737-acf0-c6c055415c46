'use client';

import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import { motion, AnimatePresence } from 'framer-motion';
import {
    FaTimes,
    FaChevronLeft,
    FaChevronRight,
    FaSpinner,
    FaTh,
    FaList,
    FaSearch,
    FaFilter,
    FaSort,
    FaSortUp,
    FaSortDown
} from 'react-icons/fa';
import { FaBox } from 'react-icons/fa6';
import PublicProductCard from '@/app/components/Cards/PublicProductCard';

export async function getBrandAllProducts(id, page = 1, perPage = 12, search = '', sortBy = 'name', sortOrder = 'asc', filters = {}) {
    console.log('🔄 Fetching brand products:', { id, page, search, sortBy, sortOrder, filters });

    try {
        // Build query parameters using URLSearchParams for proper encoding
        const params = new URLSearchParams({
            page: page.toString(),
            per_page: perPage.toString(),
            sort: sortBy,
            order: sortOrder,
        });

        // Add optional parameters
        if (search && search.trim()) {
            params.set('search', search.trim());
        }

        // Add filter parameters (matching working CategoryProductsClient pattern)
        if (filters.minPrice && filters.minPrice !== '') {
            params.set('min_price', filters.minPrice);
        }
        if (filters.maxPrice && filters.maxPrice !== '') {
            params.set('max_price', filters.maxPrice);
        }
        if (filters.brand && filters.brand !== 'all') {
            params.set('brand', filters.brand);
        }
        if (filters.inStock !== null && filters.inStock !== undefined) {
            params.set('in_stock', filters.inStock.toString());
        }

        const url = `https://b2b.instinctfusionx.xyz/public/api/v1/products/brand/${id}?${params.toString()}`;
        console.log('🌐 API URL:', url);
        console.log('📋 Query Parameters:', Object.fromEntries(params));

        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            },
            cache: 'no-store' // Ensure fresh data on each request
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log('✅ API Response:', data);
        console.log('📊 Response Structure:', {
            success: data.success,
            hasData: !!data.data,
            hasProducts: !!data.data?.products,
            productsCount: data.data?.products?.length || 0,
            hasMeta: !!data.meta,
            hasPagination: !!data.meta?.pagination
        });
        return data;
    } catch (error) {
        console.error('❌ Error fetching brand products:', error);
        throw error;
    }
}
// task!! need to review later
export default function BrandProductsClient({ brandId, brand }) {
    const [products, setProducts] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [pagination, setPagination] = useState(null);
    const [currentPage, setCurrentPage] = useState(1);
    const [viewMode, setViewMode] = useState('grid'); // 'grid' or 'list'

    // Search, Filter, and Sort states
    const [searchQuery, setSearchQuery] = useState('');
    const [sortBy, setSortBy] = useState('name');
    const [sortOrder, setSortOrder] = useState('asc');
    const [filters, setFilters] = useState({
        minPrice: '',
        maxPrice: '',
        brand: 'all',
        inStock: null // null, true, false
    });
    const [showFilters, setShowFilters] = useState(false);

    useEffect(() => {
        async function fetchProducts() {
            try {
                setLoading(true);
                setError(null);

                console.log('🔄 Fetching products with params:', {
                    brandId,
                    currentPage,
                    searchQuery,
                    sortBy,
                    sortOrder,
                    filters
                });

                const data = await getBrandAllProducts(
                    brandId,
                    currentPage,
                    12, // perPage
                    searchQuery,
                    sortBy,
                    sortOrder,
                    filters
                );

                console.log('✅ Products fetched successfully:', data);

                if (data && data.success && data.data) {
                    setProducts(data.data.products || []);
                    setPagination(data.meta?.pagination || null);
                } else {
                    console.warn('⚠️ Unexpected API response format:', data);
                    setProducts([]);
                    setPagination(null);
                }

                setLoading(false);
            } catch (error) {
                console.error('❌ Error fetching products:', error);
                setError(error.message || 'Failed to fetch products');
                setProducts([]);
                setPagination(null);
                setLoading(false);
            }
        }

        fetchProducts();
    }, [brandId, currentPage, searchQuery, sortBy, sortOrder, filters]);

    // Handle page change
    const handlePageChange = (page) => {
        if (page >= 1 && page <= (pagination?.last_page || 1)) {
            setCurrentPage(page);
        }
    };

    // Debounced search to prevent too many API calls
    const [searchInput, setSearchInput] = useState('');

    useEffect(() => {
        const timeoutId = setTimeout(() => {
            setSearchQuery(searchInput);
            setCurrentPage(1);
        }, 500); // 500ms delay

        return () => clearTimeout(timeoutId);
    }, [searchInput]);

    // Handle search input change
    const handleSearchInput = (query) => {
        setSearchInput(query);
    };

    // Handle immediate search (for clear button)
    const handleSearch = (query) => {
        setSearchInput(query);
        setSearchQuery(query);
        setCurrentPage(1);
    };

    // Handle sort change
    const handleSort = (field) => {
        if (sortBy === field) {
            // Toggle sort order if same field
            setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
        } else {
            // Set new field with ascending order
            setSortBy(field);
            setSortOrder('asc');
        }
        setCurrentPage(1); // Reset to first page when sorting
    };

    // Handle filter change
    const handleFilterChange = (filterType, value) => {
        setFilters(prev => ({
            ...prev,
            [filterType]: value
        }));
        setCurrentPage(1); // Reset to first page when filtering
    };

    // Clear all filters
    const clearFilters = () => {
        setFilters({
            minPrice: '',
            maxPrice: '',
            brand: 'all',
            inStock: null
        });
        setSearchInput('');
        setSearchQuery('');
        setSortBy('name');
        setSortOrder('asc');
        setCurrentPage(1);
    };

    // Generate page numbers for pagination
    const getPageNumbers = () => {
        if (!pagination) return [];

        const { current_page, last_page } = pagination;
        const pages = [];

        // Always show first page
        if (last_page > 1) {
            pages.push(1);
        }

        // Show pages around current page
        const start = Math.max(2, current_page - 1);
        const end = Math.min(last_page - 1, current_page + 1);

        // Add ellipsis if there's a gap
        if (start > 2) {
            pages.push('...');
        }

        // Add middle pages
        for (let i = start; i <= end; i++) {
            if (i !== 1 && i !== last_page) {
                pages.push(i);
            }
        }

        // Add ellipsis if there's a gap
        if (end < last_page - 1) {
            pages.push('...');
        }

        // Always show last page
        if (last_page > 1) {
            pages.push(last_page);
        }

        return pages;
    };

    return (
        <section className='text-gray-600 bg-blue-50 min-h-screen'>
            <div className="bg-white rounded-lg shadow-sm p-8">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
                    <h2 className="text-2xl font-bold flex items-center gap-2">
                        <FaBox className="w-6 h-6 text-orange-500" />
                        Brand Products - Search, Filter & Sort
                    </h2>

                    {/* View Mode Toggle */}
                    <div className="flex items-center gap-2">
                        <span className="text-sm font-medium text-gray-700">View:</span>
                        <button
                            onClick={() => setViewMode('grid')}
                            className={`p-3 rounded-lg transition-colors ${viewMode === 'grid'
                                ? 'bg-orange-400 text-white'
                                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                                }`}
                            title="Grid View"
                        >
                            <FaTh />
                        </button>
                        <button
                            onClick={() => setViewMode('list')}
                            className={`p-3 rounded-lg transition-colors ${viewMode === 'list'
                                ? 'bg-orange-400 text-white'
                                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                                }`}
                            title="List View"
                        >
                            <FaList />
                        </button>
                    </div>
                </div>
            </div>

            {/* Search, Filter, and Sort Controls */}
            <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
                {/* Search Bar */}
                <div className="flex flex-col lg:flex-row lg:items-center gap-4 mb-4">
                    <div className="flex-1">
                        <div className="relative">
                            <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                            <input
                                type="text"
                                placeholder="Search products by name, description..."
                                value={searchInput}
                                onChange={(e) => handleSearchInput(e.target.value)}
                                className="w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-400 focus:border-transparent"
                            />
                            {searchInput !== searchQuery && searchInput && (
                                <FaSpinner className="absolute right-3 top-1/2 transform -translate-y-1/2 text-orange-400 animate-spin" />
                            )}
                        </div>
                    </div>

                    {/* Filter Toggle */}
                    {/* <button
                        onClick={() => setShowFilters(!showFilters)}
                        className={`flex items-center gap-2 px-4 py-3 rounded-lg transition-colors ${showFilters
                            ? 'bg-orange-400 text-white'
                            : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                            }`}
                    >
                        <FaFilter />
                        Filters
                    </button> */}

                    {/* Clear Filters */}
                    {/* <button
                        onClick={clearFilters}
                        className="flex items-center gap-2 px-4 py-3 bg-red-100 text-red-700 rounded-lg hover:bg-red-200 transition-colors"
                    >
                        <FaTimes />
                        Clear All
                    </button> */}
                </div>

                {/* Sort Options */}
                {/* <div className="flex flex-wrap items-center gap-2 mb-4">
                    <span className="text-sm font-medium text-gray-700">Sort by:</span>
                    {[
                        { field: 'name', label: 'Name' },
                        { field: 'price', label: 'Price' },
                        { field: 'created_at', label: 'Date Added' },
                        { field: 'id', label: 'ID' }
                    ].map(({ field, label }) => (
                        <button
                            key={field}
                            onClick={() => handleSort(field)}
                            className={`flex items-center gap-1 px-3 py-2 rounded-lg text-sm transition-colors ${sortBy === field
                                ? 'bg-orange-400 text-white'
                                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                                }`}
                        >
                            {label}
                            {sortBy === field && (
                                sortOrder === 'asc' ? <FaSortUp /> : <FaSortDown />
                            )}
                        </button>
                    ))}
                </div> */}

                {/* Filters Panel */}
                <AnimatePresence>
                    {showFilters && (
                        <motion.div
                            initial={{ opacity: 0, height: 0 }}
                            animate={{ opacity: 1, height: 'auto' }}
                            exit={{ opacity: 0, height: 0 }}
                            className="border-t border-gray-200 pt-4"
                        >
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                {/* Price Range Filter */}
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Price Range
                                    </label>
                                    <div className="flex gap-2">
                                        <input
                                            type="number"
                                            placeholder="Min"
                                            value={filters.minPrice}
                                            onChange={(e) => handleFilterChange('minPrice', e.target.value)}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-400 focus:border-transparent"
                                        />
                                        <input
                                            type="number"
                                            placeholder="Max"
                                            value={filters.maxPrice}
                                            onChange={(e) => handleFilterChange('maxPrice', e.target.value)}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-400 focus:border-transparent"
                                        />
                                    </div>
                                </div>

                                {/* Stock Status Filter */}
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Stock Status
                                    </label>
                                    <select
                                        value={filters.inStock === null ? '' : filters.inStock.toString()}
                                        onChange={(e) => handleFilterChange('inStock',
                                            e.target.value === '' ? null : e.target.value === 'true'
                                        )}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-400 focus:border-transparent"
                                    >
                                        <option value="">All Products</option>
                                        <option value="true">In Stock</option>
                                        <option value="false">Out of Stock</option>
                                    </select>
                                </div>

                                {/* Active Filters Display */}
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Active Filters
                                    </label>
                                    <div className="flex flex-wrap gap-2">
                                        {searchQuery && (
                                            <span className="inline-flex items-center gap-1 px-2 py-1 bg-orange-100 text-orange-800 text-xs rounded-full">
                                                Search: "{searchQuery}"
                                                <button onClick={() => handleSearch('')}>
                                                    <FaTimes className="text-xs" />
                                                </button>
                                            </span>
                                        )}
                                        {searchInput !== searchQuery && searchInput && (
                                            <span className="inline-flex items-center gap-1 px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full">
                                                Typing: "{searchInput}" (searching...)
                                            </span>
                                        )}
                                        {(filters.minPrice || filters.maxPrice) && (
                                            <span className="inline-flex items-center gap-1 px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                                                Price: ${filters.minPrice || '0'} - ${filters.maxPrice || '∞'}
                                                <button onClick={() => {
                                                    handleFilterChange('minPrice', '');
                                                    handleFilterChange('maxPrice', '');
                                                }}>
                                                    <FaTimes className="text-xs" />
                                                </button>
                                            </span>
                                        )}
                                        {filters.inStock !== null && (
                                            <span className="inline-flex items-center gap-1 px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                                                {filters.inStock ? 'In Stock' : 'Out of Stock'}
                                                <button onClick={() => handleFilterChange('inStock', null)}>
                                                    <FaTimes className="text-xs" />
                                                </button>
                                            </span>
                                        )}
                                    </div>
                                </div>
                            </div>
                        </motion.div>
                    )}
                </AnimatePresence>
            </div>

            {/* Loading State */}
            {loading && (
                <div className="flex items-center justify-center py-12">
                    <div className="flex items-center gap-3">
                        <FaSpinner className="animate-spin text-orange-400 text-xl" />
                        <span className="text-lg font-medium text-gray-600">Loading products...</span>
                    </div>
                </div>
            )}

            {/* Error State */}
            {error && !loading && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
                    <div className="text-red-600 mb-2">
                        <FaTimes className="mx-auto text-2xl mb-2" />
                        <h3 className="text-lg font-semibold">Error Loading Products</h3>
                    </div>
                    <p className="text-red-700 mb-4">{error}</p>
                    <button
                        onClick={() => window.location.reload()}
                        className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
                    >
                        Try Again
                    </button>
                </div>
            )}

            {/* Products Display */}
            {!loading && !error && (
                <>
                    {viewMode === 'grid' ? (
                        /* Grid View */
                        <ul className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 list-none">
                            {Array.isArray(products) && products.map((product) => (
                                <motion.div
                                    key={product?.id || Math.random()}
                                    initial={{ opacity: 0, y: 20 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden"
                                >
                                    <PublicProductCard product={product} viewMode="grid" />
                                </motion.div>
                            ))}
                        </ul>
                    ) : (
                        /* List View */
                        <div className="space-y-4">
                            {Array.isArray(products) && products.map((product) => (
                                <motion.div
                                    key={product?.id || Math.random()}
                                    initial={{ opacity: 0, x: -20 }}
                                    animate={{ opacity: 1, x: 0 }}
                                    className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden"
                                >
                                    <PublicProductCard product={product} viewMode="list" />
                                </motion.div>
                            ))}
                        </div>
                    )}
                </>
            )}

            {/* Pagination */}
            {!loading && !error && pagination && pagination.last_page > 1 && (
                <div className="mt-8 bg-white rounded-lg shadow-sm p-6">
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                        {/* Pagination Info */}
                        <div className="text-sm text-gray-700">
                            Showing <span className="font-medium">{pagination.from}</span> to{' '}
                            <span className="font-medium">{pagination.to}</span> of{' '}
                            <span className="font-medium">{pagination.total}</span> products
                        </div>

                        {/* Pagination Controls */}
                        <div className="flex items-center gap-2">
                            {/* Previous Button */}
                            <button
                                onClick={() => handlePageChange(pagination.current_page - 1)}
                                disabled={pagination.current_page === 1}
                                className={`flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${pagination.current_page === 1
                                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                                    : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
                                    }`}
                            >
                                <FaChevronLeft className="text-xs" />
                                Previous
                            </button>

                            {/* Page Numbers */}
                            <div className="flex items-center gap-1">
                                {getPageNumbers().map((page, index) => (
                                    <button
                                        key={index}
                                        onClick={() => typeof page === 'number' && handlePageChange(page)}
                                        disabled={page === '...'}
                                        className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${page === pagination.current_page
                                            ? 'bg-orange-400 text-white'
                                            : page === '...'
                                                ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                                                : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
                                            }`}
                                    >
                                        {page}
                                    </button>
                                ))}
                            </div>

                            {/* Next Button */}
                            <button
                                onClick={() => handlePageChange(pagination.current_page + 1)}
                                disabled={pagination.current_page === pagination.last_page}
                                className={`flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${pagination.current_page === pagination.last_page
                                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                                    : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
                                    }`}
                            >
                                Next
                                <FaChevronRight className="text-xs" />
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </section>
    )
}
