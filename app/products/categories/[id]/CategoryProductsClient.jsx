'use client';

import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import { motion, AnimatePresence } from 'framer-motion';
import {
    FaTh,
    FaList,
    FaSort,
    FaSortUp,
    FaSortDown,
    FaFilter,
    FaTimes,
    FaSearch,
    FaShoppingCart,
    FaEye,
    FaChevronLeft,
    FaChevronRight,
    FaGrid3X3,
    FaColumns,
    FaSpinner,
    FaStar,
    FaStarHalfAlt,
    FaRegStar
} from 'react-icons/fa';
import toast from 'react-hot-toast';
import { FaProductHunt } from 'react-icons/fa6';
import PublicProductCard from '@/app/components/Cards/PublicProductCard';

// API function for category products
async function getCategoryProducts(categoryId, page = 1, perPage = 12, search = '', sortBy = 'name', sortOrder = 'asc', filters = {}) {
    console.log('🔄 Client: Fetching category products', { categoryId, page, search, sortBy, sortOrder, filters });

    try {
        // Build query parameters
        const params = new URLSearchParams({
            page: page.toString(),
            per_page: perPage.toString(),
            sort: sortBy,
            order: sortOrder,
        });

        // Add optional parameters
        if (search) params.set('search', search);
        if (filters.brand && filters.brand !== 'all') params.set('brand', filters.brand);
        if (filters.minPrice) params.set('min_price', filters.minPrice);
        if (filters.maxPrice) params.set('max_price', filters.maxPrice);

        const url = `https://b2b.instinctfusionx.xyz/public/api/v1/products/category/${categoryId}?${params.toString()}`;
        console.log('🌐 Client: API URL:', url);

        const response = await fetch(url, {
            cache: 'no-store', // Always fetch fresh data for client-side requests
        });

        if (!response.ok) {
            throw new Error(`API request failed: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        console.log('✅ Client: API response received:', data.success);

        return data;

    } catch (error) {
        console.error('❌ Client: Error fetching category products:', error);

        // Return mock data as fallback
        await new Promise(resolve => setTimeout(resolve, 500));

        // Return mock data (you can replace this with actual API call)
        return {
            "success": true,
            "data": {
                "products": {
                    "current_page": page,
                    "data": [
                        {
                            "id": 99,
                            "name": `Category ${categoryId} Product ${page}-1`,
                            "slug": `category-${categoryId}-product-${page}-1`,
                            "sku": `CAT${categoryId}-P${page}-001`,
                            "price": "129.99",
                            "special_price": page === 1 ? "119.99" : null,
                            "currency": "AUD",
                            "image": "https://b2b.instinctfusionx.xyz/storage/app/public/uploads/images/0ae2d2ff-e643-4967-9a98-7a4c5c627e85.jpeg",
                            "brand": "Category Brand",
                            "rating": null,
                            "review_count": null,
                            "is_active": true,
                            "approval_status": "approved",
                            "pack_price": {
                                "number_of_products": 1,
                                "per_pack_price": "129.99",
                                "per_pack_special_price": page === 1 ? "119.99" : null,
                                "customer_margin": "15.00",
                                "partner_margin": "10.00",
                                "customer_margin_type": "percentage",
                                "partner_margin_type": "percentage",
                                "delivery_fee": null
                            },
                            "bulk_prices": []
                        },
                        {
                            "id": 98,
                            "name": `Category ${categoryId} Product ${page}-2`,
                            "slug": `category-${categoryId}-product-${page}-2`,
                            "sku": `CAT${categoryId}-P${page}-002`,
                            "price": "89.99",
                            "special_price": null,
                            "currency": "AUD",
                            "image": "",
                            "brand": "Category Brand",
                            "rating": null,
                            "review_count": null,
                            "is_active": true,
                            "approval_status": "approved",
                            "pack_price": {
                                "number_of_products": 1,
                                "per_pack_price": "89.99",
                                "per_pack_special_price": null,
                                "customer_margin": "15.00",
                                "partner_margin": "10.00",
                                "customer_margin_type": "percentage",
                                "partner_margin_type": "percentage",
                                "delivery_fee": null
                            },
                            "bulk_prices": []
                        }
                    ],
                    "first_page_url": `https://b2b.instinctfusionx.xyz/public/api/v1/products/category/${categoryId}?page=1`,
                    "from": ((page - 1) * perPage) + 1,
                    "last_page": 3,
                    "last_page_url": `https://b2b.instinctfusionx.xyz/public/api/v1/products/category/${categoryId}?page=3`,
                    "links": [
                        {
                            "url": page > 1 ? `https://b2b.instinctfusionx.xyz/public/api/v1/products/category/${categoryId}?page=${page - 1}` : null,
                            "label": "&laquo; Previous",
                            "active": false
                        },
                        {
                            "url": `https://b2b.instinctfusionx.xyz/public/api/v1/products/category/${categoryId}?page=1`,
                            "label": "1",
                            "active": page === 1
                        },
                        {
                            "url": `https://b2b.instinctfusionx.xyz/public/api/v1/products/category/${categoryId}?page=2`,
                            "label": "2",
                            "active": page === 2
                        },
                        {
                            "url": `https://b2b.instinctfusionx.xyz/public/api/v1/products/category/${categoryId}?page=3`,
                            "label": "3",
                            "active": page === 3
                        },
                        {
                            "url": page < 3 ? `https://b2b.instinctfusionx.xyz/public/api/v1/products/category/${categoryId}?page=${page + 1}` : null,
                            "label": "Next &raquo;",
                            "active": false
                        }
                    ],
                    "next_page_url": page < 3 ? `https://b2b.instinctfusionx.xyz/public/api/v1/products/category/${categoryId}?page=${page + 1}` : null,
                    "path": `https://b2b.instinctfusionx.xyz/public/api/v1/products/category/${categoryId}`,
                    "per_page": perPage,
                    "prev_page_url": page > 1 ? `https://b2b.instinctfusionx.xyz/public/api/v1/products/category/${categoryId}?page=${page - 1}` : null,
                    "to": Math.min(page * perPage, 25),
                    "total": 25
                }
            }
        };
    }
}

export default function CategoryProductsClient({ initialData, categoryId, categoryInfo, initialSearchParams }) {
    console.log('🎯 Client: CategoryProductsClient mounted');
    console.log('📊 Client: Initial data:', initialData);
    console.log('🏷️ Client: Category ID:', categoryId);
    console.log('ℹ️ Client: Category info:', categoryInfo);

    const router = useRouter();
    const searchParams = useSearchParams();

    // State management
    const [products, setProducts] = useState(initialData?.data || []);
    const [pagination, setPagination] = useState(initialData);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);

    // UI state
    const [viewMode, setViewMode] = useState('grid');
    const [gridColumns, setGridColumns] = useState(3);
    const [showFilters, setShowFilters] = useState(false);

    // Search and filter state
    const [searchQuery, setSearchQuery] = useState(initialSearchParams?.search || '');
    const [sortBy, setSortBy] = useState(initialSearchParams?.sort || 'name');
    const [sortOrder, setSortOrder] = useState(initialSearchParams?.order || 'asc');
    const [filters, setFilters] = useState({
        brand: initialSearchParams?.brand || 'all',
        minPrice: initialSearchParams?.min_price || '',
        maxPrice: initialSearchParams?.max_price || ''
    });

    // Pagination state
    const [currentPage, setCurrentPage] = useState(parseInt(initialSearchParams?.page) || 1);
    const [perPage, setPerPage] = useState(parseInt(initialSearchParams?.per_page) || 12);

    // Get unique brands for filters
    const brands = useMemo(() => {
        const allBrands = products.map(p => p.brand);
        return ['all', ...new Set(allBrands)];
    }, [products]);

    console.log('🔧 Client: Current state:', {
        productsCount: products.length,
        currentPage,
        searchQuery,
        sortBy,
        sortOrder,
        filters
    });

    // Fetch products function
    const fetchProducts = useCallback(async () => {
        setLoading(true);
        setError(null);

        try {
            console.log('🔄 Client: Fetching products for category:', categoryId);

            const response = await getCategoryProducts(
                categoryId,
                currentPage,
                perPage,
                searchQuery,
                sortBy,
                sortOrder,
                filters
            );

            if (response.success) {
                setProducts(response.data.products.data);
                setPagination(response.data.products);
                console.log('✅ Client: Products updated:', response.data.products.data.length);
            } else {
                setError('Failed to fetch products');
            }
        } catch (err) {
            setError('An error occurred while fetching products');
            console.error('❌ Client: Error fetching products:', err);
        } finally {
            setLoading(false);
        }
    }, [categoryId, currentPage, perPage, searchQuery, sortBy, sortOrder, filters]);

    // Handle page change
    const handlePageChange = useCallback((page) => {
        // Validate page number
        if (!page || page < 1) {
            page = 1;
        }

        // Don't change if it's the same page
        if (page === currentPage) {
            return;
        }

        // Check if page exists in pagination
        if (pagination && page > pagination.last_page) {
            page = pagination.last_page;
        }

        console.log('📄 Client: Changing page from', currentPage, 'to', page);
        setCurrentPage(page);

        // Update URL
        const params = new URLSearchParams(searchParams);
        params.set('page', page.toString());
        router.push(`?${params.toString()}`);
    }, [router, searchParams, currentPage, pagination]);

    // Handle search
    const handleSearch = useCallback((query) => {
        setSearchQuery(query);
        setCurrentPage(1);
        const params = new URLSearchParams(searchParams);
        if (query) {
            params.set('search', query);
        } else {
            params.delete('search');
        }
        params.set('page', '1');
        router.push(`?${params.toString()}`);
    }, [router, searchParams]);

    // Handle sort
    const handleSort = useCallback((field) => {
        const newOrder = sortBy === field && sortOrder === 'asc' ? 'desc' : 'asc';
        setSortBy(field);
        setSortOrder(newOrder);
        setCurrentPage(1);

        const params = new URLSearchParams(searchParams);
        params.set('sort', field);
        params.set('order', newOrder);
        params.set('page', '1');
        router.push(`?${params.toString()}`);
    }, [sortBy, sortOrder, router, searchParams]);

    // Handle filter change
    const handleFilterChange = useCallback((filterType, value) => {
        setFilters(prev => ({ ...prev, [filterType]: value }));
        setCurrentPage(1);

        const params = new URLSearchParams(searchParams);
        if (value && value !== 'all') {
            params.set(filterType, value);
        } else {
            params.delete(filterType);
        }
        params.set('page', '1');
        router.push(`?${params.toString()}`);
    }, [router, searchParams]);

    // Effect to fetch products when dependencies change
    useEffect(() => {
        // Only fetch if not using initial SSR data or if parameters changed
        const urlPage = parseInt(searchParams.get('page')) || 1;
        const urlSearch = searchParams.get('search') || '';
        const urlSort = searchParams.get('sort') || 'name';
        const urlOrder = searchParams.get('order') || 'asc';

        // Check if current state matches URL parameters
        const stateChanged =
            currentPage !== urlPage ||
            searchQuery !== urlSearch ||
            sortBy !== urlSort ||
            sortOrder !== urlOrder;

        if (stateChanged) {
            console.log('🔄 Client: State changed, fetching new data');
            fetchProducts();
        }
    }, [currentPage, searchQuery, sortBy, sortOrder, filters, fetchProducts, searchParams]);

    return (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div className="text-center mb-8">
                <h2 className="text-xl font-semibold text-gray-900 mb-2">
                    Products in {categoryInfo.name}
                </h2>
                <p className="text-gray-600">
                    {pagination?.total || 0} products found
                </p>

                {/* Debug Info */}
                {/* <div className="mt-4 p-4 bg-blue-50 rounded-lg text-sm text-blue-800">
                    <h3 className="font-semibold mb-2">🔍 SSR Debug Information:</h3>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs">
                        <div><strong>Category ID:</strong> {categoryId}</div>
                        <div><strong>Current Page:</strong> {currentPage}</div>
                        <div><strong>Total Products:</strong> {pagination?.total || 0}</div>
                        <div><strong>Per Page:</strong> {perPage}</div>
                        <div><strong>Search Query:</strong> {searchQuery || 'None'}</div>
                        <div><strong>Sort:</strong> {sortBy} ({sortOrder})</div>
                        <div><strong>Brand Filter:</strong> {filters.brand}</div>
                        <div><strong>SSR Data:</strong> ✅ Loaded</div>
                    </div>
                </div> */}
            </div>

            {/* Products Grid */}
            <ul className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 list-none">
                {products.map((product) => (
                    <motion.div
                        key={product.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden"
                    >
                        <PublicProductCard product={product} viewMode={'grid'} />
                    </motion.div>
                ))}
            </ul>

            {/* No Products */}
            {products.length === 0 && !loading && (
                <div className="text-center py-12">
                    <FaSearch className="text-gray-400 text-4xl mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No products found</h3>
                    <p className="text-gray-600">No products available in this category.</p>

                    <button>
                        <Link href="/products" className="bg-orange-400 text-white px-6 py-3 rounded-lg hover:bg-orange-500 flex items-center gap-2 transition-colors">
                            <p><FaProductHunt className="text-2xl" /></p>
                            <p>Browse All Products</p>
                        </Link>
                    </button>
                </div>
            )}

            {/* Loading State */}
            {loading && (
                <div className="text-center py-12">
                    <FaSpinner className="animate-spin text-orange-400 text-4xl mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">Loading products...</h3>
                    <p className="text-gray-600">Please wait while we fetch the latest products.</p>
                </div>
            )}

            {/* Error State */}
            {error && (
                <div className="text-center py-12">
                    <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md mx-auto">
                        <h3 className="text-lg font-medium text-red-800 mb-2">Error Loading Products</h3>
                        <p className="text-red-600 mb-4">{error}</p>
                        <button
                            onClick={fetchProducts}
                            className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
                        >
                            Try Again
                        </button>
                    </div>
                </div>
            )}

            {/* Pagination */}
            {pagination && pagination.last_page > 1 && !loading && (
                <div className="flex flex-col items-center gap-6 mt-12">
                    {/* Pagination Controls */}
                    <div className="flex items-center gap-2">
                        {/* Previous Button */}
                        <button
                            onClick={() => handlePageChange(currentPage - 1)}
                            disabled={currentPage <= 1 || !pagination.prev_page_url}
                            className="px-4 py-2 rounded-xl bg-white border border-gray-200 text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2 transition-all"
                        >
                            <FaChevronLeft className="text-xs" />
                            Previous
                        </button>

                        {/* Page Numbers */}
                        <div className="flex items-center gap-1 mx-4">
                            {pagination.links && pagination.links
                                .filter(link => {
                                    // Filter out Previous/Next and invalid labels
                                    return link.label !== '&laquo; Previous' &&
                                        link.label !== 'Next &raquo;' &&
                                        !isNaN(parseInt(link.label));
                                })
                                .map((link, index) => {
                                    const pageNum = parseInt(link.label);

                                    return (
                                        <button
                                            key={`page-${pageNum}-${index}`}
                                            onClick={() => handlePageChange(pageNum)}
                                            className={`w-10 h-10 rounded-xl text-sm font-medium transition-all ${pageNum === currentPage || link.active
                                                ? 'bg-orange-400 text-white shadow-lg'
                                                : 'bg-white border border-gray-200 text-gray-700 hover:bg-gray-50'
                                                }`}
                                            disabled={pageNum === currentPage}
                                        >
                                            {pageNum}
                                        </button>
                                    );
                                })}
                        </div>

                        {/* Next Button */}
                        <button
                            onClick={() => handlePageChange(currentPage + 1)}
                            disabled={currentPage >= pagination.last_page || !pagination.next_page_url}
                            className="px-4 py-2 rounded-xl bg-white border border-gray-200 text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2 transition-all"
                        >
                            Next
                            <FaChevronRight className="text-xs" />
                        </button>
                    </div>

                    {/* Pagination Info */}
                    <div className="text-sm text-gray-500">
                        Showing {pagination.from || 1} to {pagination.to || pagination.per_page} of {pagination.total || 0} products
                        <span className="ml-4 text-xs text-gray-400">
                            (Page {currentPage} of {pagination.last_page})
                        </span>
                    </div>

                    {/* Per Page Selector */}
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                        <span>Show:</span>
                        <select
                            value={perPage}
                            onChange={(e) => {
                                const newPerPage = parseInt(e.target.value);
                                setPerPage(newPerPage);
                                setCurrentPage(1);
                                const params = new URLSearchParams(searchParams);
                                params.set('per_page', newPerPage.toString());
                                params.set('page', '1');
                                router.push(`?${params.toString()}`);
                            }}
                            className="border border-gray-300 rounded-lg px-3 py-1 bg-white focus:ring-2 focus:ring-orange-400 focus:border-orange-400"
                        >
                            <option value={12}>12 per page</option>
                            <option value={24}>24 per page</option>
                            <option value={48}>48 per page</option>
                            <option value={96}>96 per page</option>
                        </select>
                    </div>
                </div>
            )}
        </div>
    );
}
