import React, { Suspense } from 'react';
import AllPartners from '../components/Partners/AllPartners';

// Loading component for Suspense fallback
function PartnersLoading() {
    return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
            <div className="text-center">
                <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-orange-500 mx-auto mb-4"></div>
                <p className="text-gray-600">Loading partners...</p>
            </div>
        </div>
    );
}

export default function PartnersPage() {
    return (
        <section className="min-h-screen flex flex-col items-center justify-center bg-gray-50 px-4">
            <Suspense fallback={<PartnersLoading />}>
                <AllPartners />
            </Suspense>
        </section>
    );
}
