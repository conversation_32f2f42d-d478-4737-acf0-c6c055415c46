import Login from '@/app/components/Authentication/Login'
import React, { Suspense } from 'react'

// Loading component for Suspense fallback
function LoginLoading() {
    return (
        <div className="min-h-screen flex items-center justify-center">
            <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-orange-500"></div>
        </div>
    );
}

export default function page() {
    return (
        <section>
            <Suspense fallback={<LoginLoading />}>
                <Login title="Welcome back to Diptouch"
                    subHeading="Select your login and access your SEO dashboard"
                    description={""}
                    partner={false}
                    user={false}
                    seo={true}
                    userType="seo"
                />
            </Suspense>
        </section>
    )
}
