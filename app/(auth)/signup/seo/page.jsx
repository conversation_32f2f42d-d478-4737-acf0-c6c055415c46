import Signup from '@/app/components/Authentication/Signup'
import React, { Suspense } from 'react'

// Loading component for Suspense fallback
function SignupLoading() {
    return (
        <div className="min-h-screen flex items-center justify-center">
            <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-orange-500"></div>
        </div>
    );
}

export default function SignupSEOPage() {
    return (
        <section>
            <Suspense fallback={<SignupLoading />}>
                <Signup title="Join Diptouch"
                    subHeading="Join as a SEO Expert and help us improve our website"
                    description=""
                    partner={false}
                    user={false}
                    userType="seo"
                />
            </Suspense>
        </section>
    )
}
