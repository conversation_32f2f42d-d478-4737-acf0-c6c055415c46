
import { Suspense } from 'react';
import Navbar from './components/LandingPage/Navbar';
import HeroSection from './components/LandingPage/HeroSection';
import HeroSectionSkeleton from './components/LandingPage/HeroSectionSkeleton';
import PalletIntro from './components/LandingPage/PalletIntro';
import CategoryGrid from './components/LandingPage/CategoryGrid';
import FeaturedProducts from './components/LandingPage/FeaturedProducts';
import QuickOrder from './components/LandingPage/QuickOrder';
import FilterBar from './components/LandingPage/FilterBar';
import HowItWorks from './components/LandingPage/HowItWorks';
import WhyChooseUs from './components/LandingPage/WhyChooseUs';
import Highlights from './components/LandingPage/Highlights';
import CtaBanner from './components/LandingPage/CtaBanner';
import Testimonials from './components/LandingPage/Testimonials';
import AutoReorder from './components/LandingPage/AutoReorder';
import Newsletter from './components/LandingPage/Newsletter';
import ProductList from './components/LandingPage/ProductList';
import Footer from './components/LandingPage/Footer';
import NewHeroSection from './components/LandingPage/NewHeroSection';

export default function Home() {
  return (
    <div className="min-h-screen bg-background font-[family-name:var(--font-geist-sans)]">
      <Navbar />
      <main>
        <ProductList />
        {/* Wrap HeroSection in Suspense to handle useSearchParams() */}
        <Suspense fallback={<HeroSectionSkeleton />}>
          {/* <HeroSection /> */}
          <NewHeroSection />
        </Suspense>
        <FeaturedProducts />
        <CategoryGrid />
        {/* <QuickOrder />  */}
        {/* <FilterBar /> */}

        <HowItWorks />
        <WhyChooseUs />
        {/* <Highlights /> */}

        <CtaBanner />
        <Testimonials />
        <PalletIntro />
        <AutoReorder />
        <Newsletter />
      </main>
      <Footer />
    </div>
  );
}
