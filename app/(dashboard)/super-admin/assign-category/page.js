'use client';

import React, { useState, useEffect } from 'react';
import { DragDropContext, Droppable, Draggable } from '@hello-pangea/dnd';
import { motion } from 'framer-motion';
import {
    MagnifyingGlassIcon,
    CheckCircleIcon,
    XMarkIcon,
    ArrowPathIcon,
    TagIcon,
    ShoppingBagIcon,
    PlusCircleIcon,
    ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import { FaBoxes, FaTags, FaArrowsAlt } from 'react-icons/fa';
import Swal from 'sweetalert2';

export default function AssignCategoryPage() {
    // State management
    const [products, setProducts] = useState([]);
    const [categories, setCategories] = useState([]);
    const [selectedProducts, setSelectedProducts] = useState([]);
    const [selectedCategories, setSelectedCategories] = useState([]);
    const [targetedProducts, setTargetedProducts] = useState([]);
    const [selectedCategoriesBox, setSelectedCategoriesBox] = useState([]);
    const [loading, setLoading] = useState(true);
    const [loadingStatus, setLoadingStatus] = useState('');
    const [error, setError] = useState(null);
    const [searchProduct, setSearchProduct] = useState('');
    const [searchCategory, setSearchCategory] = useState('');
    const [assignmentHistory, setAssignmentHistory] = useState([]);
    const [operationMode, setOperationMode] = useState('assign'); // 'assign' or 'deassign'
    const [productCategories, setProductCategories] = useState({}); // Track existing categories for each product

    // Fetch individual product details including categories
    const fetchProductDetails = async (productId) => {
        try {
            const token = localStorage.getItem('superAdminAuthToken');
            if (!token) {
                throw new Error('No authentication token found');
            }

            const response = await fetch(`${process.env.NEXT_PUBLIC_WEB_SERVER_URL}/superadmin/products/${productId}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            if (data.success && data.data && data.data.product) {
                return data.data.product;
            }
            throw new Error('Invalid product data structure');
        } catch (error) {
            console.error(`Error fetching product ${productId} details:`, error);
            return null;
        }
    };

    // Fetch categories for multiple products and store them
    const fetchProductsCategories = async (productIds) => {
        console.log('🔄 Fetching categories for products:', productIds);
        setLoadingStatus('Fetching product categories...');

        const categoriesMap = {};

        for (const productId of productIds) {
            try {
                const productDetails = await fetchProductDetails(productId);
                if (productDetails && productDetails.categories) {
                    categoriesMap[productId] = productDetails.categories.map(cat => ({
                        id: cat.id,
                        name: cat.name
                    }));
                    console.log(`✅ Found ${productDetails.categories.length} categories for product ${productId}`);
                } else {
                    // Default to General Product Category if no categories found
                    categoriesMap[productId] = [{ id: 1, name: 'General Product Category' }];
                    console.log(`⚠️ No categories found for product ${productId}, using default`);
                }
            } catch (error) {
                console.error(`❌ Error fetching categories for product ${productId}:`, error);
                categoriesMap[productId] = [{ id: 1, name: 'General Product Category' }];
            }
        }

        setProductCategories(prev => ({ ...prev, ...categoriesMap }));
        console.log('🎉 Product categories updated:', categoriesMap);
    };

    // Fetch products from API with pagination
    const fetchProducts = async () => {
        try {
            const token = localStorage.getItem('superAdminAuthToken');
            if (!token) {
                throw new Error('No authentication token found');
            }

            let allProducts = [];
            let currentPage = 1;
            let hasMorePages = true;
            let maxPages = 100; // Safety limit to prevent infinite loops

            console.log('🔄 Starting to fetch products...');
            setLoadingStatus('Fetching products...');

            while (hasMorePages && currentPage <= maxPages) {
                console.log(`📄 Fetching products page ${currentPage}...`);
                setLoadingStatus(`Fetching products page ${currentPage}...`);

                const response = await fetch(`${process.env.NEXT_PUBLIC_WEB_SERVER_URL}/superadmin/products?page=${currentPage}&per_page=100`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                console.log(`📊 Page ${currentPage} response:`, data);

                if (data.success && data.data) {
                    // Handle different response formats
                    if (data.data.products) {
                        // Format 1: data.data.products is an array (no pagination)
                        if (Array.isArray(data.data.products)) {
                            console.log(`✅ Found ${data.data.products.length} products (array format)`);
                            allProducts = [...allProducts, ...data.data.products];
                            hasMorePages = false;
                        }
                        // Format 2: data.data.products is paginated object
                        else if (data.data.products.data && Array.isArray(data.data.products.data)) {
                            console.log(`✅ Found ${data.data.products.data.length} products on page ${currentPage}`);
                            console.log(`📈 Pagination info: current_page=${data.data.products.current_page}, last_page=${data.data.products.last_page}`);

                            allProducts = [...allProducts, ...data.data.products.data];
                            hasMorePages = currentPage < data.data.products.last_page;
                            currentPage++;
                        }
                        // Format 3: data.data.products exists but no data array
                        else {
                            console.log('⚠️ Products object exists but no data array found');
                            hasMorePages = false;
                        }
                    }
                    // Format 4: Direct array in data
                    else if (Array.isArray(data.data)) {
                        console.log(`✅ Found ${data.data.length} products (direct array format)`);
                        allProducts = [...allProducts, ...data.data];
                        hasMorePages = false;
                    }
                    else {
                        console.log('⚠️ No products found in response');
                        hasMorePages = false;
                    }
                } else {
                    console.error('❌ API response not successful:', data);
                    setError('Invalid response format from server');
                    break;
                }
            }

            console.log(`🎉 Total products fetched: ${allProducts.length}`);

            // Sort products alphabetically by name (A-Z)
            const sortedProducts = allProducts.sort((a, b) =>
                a.name.toLowerCase().localeCompare(b.name.toLowerCase())
            );

            setProducts(sortedProducts);
        } catch (error) {
            console.error('❌ Error fetching products:', error);
            setError(error.message);
        }
    };

    // Fetch categories from API with pagination
    const fetchCategories = async () => {
        try {
            let allCategories = [];
            let currentPage = 1;
            let hasMorePages = true;
            let maxPages = 100; // Safety limit to prevent infinite loops

            console.log('🔄 Starting to fetch categories...');
            setLoadingStatus('Fetching categories...');

            while (hasMorePages && currentPage <= maxPages) {
                console.log(`📄 Fetching categories page ${currentPage}...`);
                setLoadingStatus(`Fetching categories page ${currentPage}...`);

                const response = await fetch(`${process.env.NEXT_PUBLIC_WEB_SERVER_URL}/categories?page=${currentPage}&per_page=100`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                console.log(`📊 Categories page ${currentPage} response:`, data);

                if (data.success && data.data) {
                    // Handle different response formats
                    if (data.data.categories) {
                        // Format 1: data.data.categories is an array (no pagination)
                        if (Array.isArray(data.data.categories)) {
                            console.log(`✅ Found ${data.data.categories.length} categories (array format)`);
                            allCategories = [...allCategories, ...data.data.categories];
                            hasMorePages = false;
                        }
                        // Format 2: data.data.categories is paginated object
                        else if (data.data.categories.data && Array.isArray(data.data.categories.data)) {
                            console.log(`✅ Found ${data.data.categories.data.length} categories on page ${currentPage}`);
                            console.log(`📈 Pagination info: current_page=${data.data.categories.current_page}, last_page=${data.data.categories.last_page}`);

                            allCategories = [...allCategories, ...data.data.categories.data];
                            hasMorePages = currentPage < data.data.categories.last_page;
                            currentPage++;
                        }
                        // Format 3: data.data.categories exists but no data array
                        else {
                            console.log('⚠️ Categories object exists but no data array found');
                            hasMorePages = false;
                        }
                    }
                    // Format 4: Direct array in data
                    else if (Array.isArray(data.data)) {
                        console.log(`✅ Found ${data.data.length} categories (direct array format)`);
                        allCategories = [...allCategories, ...data.data];
                        hasMorePages = false;
                    }
                    else {
                        console.log('⚠️ No categories found in response');
                        hasMorePages = false;
                    }
                } else {
                    console.error('❌ Categories API response not successful:', data);
                    setError('Invalid categories response format from server');
                    break;
                }
            }

            console.log(`🎉 Total categories fetched: ${allCategories.length}`);

            // Sort categories alphabetically by name (A-Z)
            const sortedCategories = allCategories.sort((a, b) =>
                a.name.toLowerCase().localeCompare(b.name.toLowerCase())
            );

            setCategories(sortedCategories);
        } catch (error) {
            console.error('❌ Error fetching categories:', error);
            setError(error.message);
        }
    };



    // Initialize data
    useEffect(() => {
        const initializeData = async () => {
            setLoading(true);
            setLoadingStatus('Initializing...');
            await Promise.all([fetchProducts(), fetchCategories()]);
            setLoadingStatus('');
            setLoading(false);
        };
        initializeData();
    }, []);

    // Handle product selection
    const handleProductSelect = (product) => {
        setSelectedProducts(prev => {
            const isSelected = prev.find(p => p.id === product.id);
            if (isSelected) {
                return prev.filter(p => p.id !== product.id);
            } else {
                return [...prev, product];
            }
        });
    };

    // Handle category selection
    const handleCategorySelect = (category) => {
        setSelectedCategories(prev => {
            const isSelected = prev.find(c => c.id === category.id);
            if (isSelected) {
                return prev.filter(c => c.id !== category.id);
            } else {
                return [...prev, category];
            }
        });
    };

    // Move selected products to targeted products box
    const moveToTargetedProducts = async () => {
        const newProducts = selectedProducts.filter(sp =>
            !targetedProducts.find(tp => tp.id === sp.id)
        );

        if (newProducts.length > 0) {
            // Fetch categories for new products
            const newProductIds = newProducts.map(p => p.id);
            await fetchProductsCategories(newProductIds);

            setTargetedProducts(prev => [...prev, ...newProducts]);
        }

        setSelectedProducts([]);
    };

    // Move selected categories to selected categories box
    const moveToSelectedCategories = () => {
        setSelectedCategoriesBox(prev => {
            const newCategories = selectedCategories.filter(sc =>
                !prev.find(tc => tc.id === sc.id)
            );
            return [...prev, ...newCategories];
        });
        setSelectedCategories([]);
    };

    // Remove from targeted products
    const removeFromTargetedProducts = (productId) => {
        setTargetedProducts(prev => prev.filter(p => p.id !== productId));
        // Also remove the product's categories from tracking
        setProductCategories(prev => {
            const updated = { ...prev };
            delete updated[productId];
            return updated;
        });
    };

    // Remove from selected categories box
    const removeFromSelectedCategoriesBox = (categoryId) => {
        setSelectedCategoriesBox(prev => prev.filter(c => c.id !== categoryId));
    };

    // Assign or De-assign categories to/from products API call
    const updateProductCategories = async (productId, newCategoryIds, mode = 'assign') => {
        try {
            const token = localStorage.getItem('superAdminAuthToken');
            if (!token) {
                throw new Error('No authentication token found');
            }

            let finalCategoryIds;

            if (mode === 'deassign') {
                // For de-assign operation, set to default category (General Product Category - ID: 1)
                finalCategoryIds = ['1'];
            } else {
                // For assign operation, merge existing categories with new ones
                const existingCategories = productCategories[productId] || [];
                const existingCategoryIds = existingCategories.map(cat => cat.id.toString());

                // Combine existing and new category IDs, removing duplicates
                const combinedCategoryIds = [...new Set([...existingCategoryIds, ...newCategoryIds])];

                // Ensure at least the General Product Category (ID: 1) is included
                if (!combinedCategoryIds.includes('1')) {
                    combinedCategoryIds.push('1');
                }

                finalCategoryIds = combinedCategoryIds;

                console.log(`🔄 Product ${productId}: Existing categories: [${existingCategoryIds.join(', ')}], New categories: [${newCategoryIds.join(', ')}], Final categories: [${finalCategoryIds.join(', ')}]`);
            }

            const response = await fetch(`${process.env.NEXT_PUBLIC_WEB_SERVER_URL}/superadmin/products/${productId}/categories`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                },
                body: JSON.stringify({
                    category_ids: finalCategoryIds
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            return data;
        } catch (error) {
            console.error(`Error ${mode === 'assign' ? 'assigning' : 'de-assigning'} categories:`, error);
            throw error;
        }
    };

    // Handle assignment interaction
    const onDragEnd = async (result) => {
        const { destination } = result;

        if (!destination) {
            return;
        }

        // If moved to the assignment area
        if (destination.droppableId === 'assignment-area') {
            try {
                // Check if we have both products and categories to assign
                if (targetedProducts.length === 0) {
                    Swal.fire({
                        title: 'No Products Selected',
                        text: 'Please select products first before assigning categories.',
                        icon: 'warning',
                        confirmButtonColor: '#f97316'
                    });
                    return;
                }

                // Only check for categories in assign mode
                if (operationMode === 'assign' && selectedCategoriesBox.length === 0) {
                    Swal.fire({
                        title: 'No Categories Selected',
                        text: 'Please select categories first before assigning.',
                        icon: 'warning',
                        confirmButtonColor: '#f97316'
                    });
                    return;
                }

                // Show confirmation dialog
                const result = await Swal.fire({
                    title: operationMode === 'assign' ? 'Confirm Assignment' : 'Confirm De-assignment',
                    html: operationMode === 'assign' ? `
                        <div class="text-left">
                            <p class="mb-2"><strong>Products (${targetedProducts.length}):</strong></p>
                            <ul class="list-disc list-inside mb-4 text-sm">
                                ${targetedProducts.slice(0, 3).map(p => `<li>${p.name}</li>`).join('')}
                                ${targetedProducts.length > 3 ? `<li>... and ${targetedProducts.length - 3} more</li>` : ''}
                            </ul>
                            <p class="mb-2"><strong>New Categories to Add (${selectedCategoriesBox.length}):</strong></p>
                            <ul class="list-disc list-inside mb-4 text-sm">
                                ${selectedCategoriesBox.slice(0, 3).map(c => `<li>${c.name}</li>`).join('')}
                                ${selectedCategoriesBox.length > 3 ? `<li>... and ${selectedCategoriesBox.length - 3} more</li>` : ''}
                            </ul>
                            <p class="mb-2 text-blue-600"><strong>ℹ️ Note: Existing categories will be preserved and new categories will be added</strong></p>
                        </div>
                    ` : `
                        <div class="text-left">
                            <p class="mb-2"><strong>Products to de-assign (${targetedProducts.length}):</strong></p>
                            <ul class="list-disc list-inside mb-4 text-sm">
                                ${targetedProducts.slice(0, 3).map(p => `<li>${p.name}</li>`).join('')}
                                ${targetedProducts.length > 3 ? `<li>... and ${targetedProducts.length - 3} more</li>` : ''}
                            </ul>
                            <p class="mb-2 text-orange-600"><strong>⚠️ This will reset all categories to "General Product Category" only</strong></p>
                        </div>
                    `,
                    icon: operationMode === 'assign' ? 'question' : 'warning',
                    showCancelButton: true,
                    confirmButtonColor: operationMode === 'assign' ? '#f97316' : '#dc2626',
                    cancelButtonColor: '#6b7280',
                    confirmButtonText: operationMode === 'assign' ? 'Yes, Assign Categories' : 'Yes, De-assign Categories',
                    cancelButtonText: 'Cancel'
                });

                if (result.isConfirmed) {
                    // Show loading
                    Swal.fire({
                        title: operationMode === 'assign' ? 'Assigning Categories...' : 'De-assigning Categories...',
                        text: operationMode === 'assign'
                            ? 'Please wait while we assign categories to products.'
                            : 'Please wait while we de-assign categories from products.',
                        allowOutsideClick: false,
                        didOpen: () => {
                            Swal.showLoading();
                        }
                    });

                    // Assign categories to each product
                    const categoryIds = selectedCategoriesBox.map(c => c.id.toString());
                    const assignments = [];

                    for (const product of targetedProducts) {
                        try {
                            const assignmentResult = await updateProductCategories(product.id, categoryIds, operationMode);
                            assignments.push({
                                product: product.name,
                                success: true,
                                result: assignmentResult
                            });
                        } catch (error) {
                            assignments.push({
                                product: product.name,
                                success: false,
                                error: error.message
                            });
                        }
                    }

                    // Show results
                    const successCount = assignments.filter(a => a.success).length;
                    const failureCount = assignments.filter(a => !a.success).length;

                    if (failureCount === 0) {
                        Swal.fire({
                            title: 'Success!',
                            text: operationMode === 'assign'
                                ? `Successfully assigned categories to ${successCount} products.`
                                : `Successfully de-assigned categories from ${successCount} products.`,
                            icon: 'success',
                            confirmButtonColor: '#f97316'
                        });

                        // Clear the boxes after successful assignment
                        setTargetedProducts([]);
                        setSelectedCategoriesBox([]);
                        setProductCategories({});

                        // Add to assignment history
                        setAssignmentHistory(prev => [...prev, {
                            timestamp: new Date(),
                            products: targetedProducts.length,
                            categories: selectedCategoriesBox.length,
                            success: true
                        }]);
                    } else {
                        const failedProducts = assignments.filter(a => !a.success).map(a => a.product);
                        Swal.fire({
                            title: 'Partial Success',
                            html: `
                                <div class="text-left">
                                    <p class="mb-2">✅ Successfully ${operationMode === 'assign' ? 'assigned' : 'de-assigned'}: ${successCount} products</p>
                                    <p class="mb-2">❌ Failed ${operationMode === 'assign' ? 'assignments' : 'de-assignments'}: ${failureCount} products</p>
                                    <p class="text-sm text-gray-600">Failed products: ${failedProducts.join(', ')}</p>
                                </div>
                            `,
                            icon: 'warning',
                            confirmButtonColor: '#f97316'
                        });
                    }
                }
            } catch (error) {
                Swal.fire({
                    title: 'Error',
                    text: operationMode === 'assign'
                        ? 'An error occurred while assigning categories. Please try again.'
                        : 'An error occurred while de-assigning categories. Please try again.',
                    icon: 'error',
                    confirmButtonColor: '#f97316'
                });
            }
        }
    };

    // Filter functions
    const filteredProducts = products.filter(product =>
        product.name.toLowerCase().includes(searchProduct.toLowerCase()) ||
        product.sku.toLowerCase().includes(searchProduct.toLowerCase())
    );

    const filteredCategories = categories.filter(category =>
        category.name.toLowerCase().includes(searchCategory.toLowerCase())
    );

    if (loading) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-orange-50 to-amber-50 p-6">
                <div className="max-w-7xl mx-auto">
                    <div className="flex items-center justify-center h-64">
                        <div className="text-center">
                            <ArrowPathIcon className="h-12 w-12 text-orange-500 animate-spin mx-auto mb-4" />
                            <p className="text-gray-800">Loading products and categories...</p>
                            {loadingStatus && (
                                <p className="text-orange-600 text-sm mt-2">{loadingStatus}</p>
                            )}
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-orange-50 to-amber-50 p-6">
                <div className="max-w-7xl mx-auto">
                    <div className="flex items-center justify-center h-64">
                        <div className="text-center">
                            <ExclamationTriangleIcon className="h-12 w-12 text-red-500 mx-auto mb-4" />
                            <p className="text-red-700 mb-4 font-medium">{error}</p>
                            <button
                                onClick={() => window.location.reload()}
                                className="px-4 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors"
                            >
                                Retry
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <DragDropContext onDragEnd={onDragEnd}>
            <div className="min-h-screen bg-gradient-to-br from-orange-50 to-amber-50 p-6">
                <div className="max-w-7xl mx-auto">
                    {/* Header Section */}
                    <motion.div
                        initial={{ opacity: 0, y: -20 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="mb-8"
                    >
                        <div className="bg-white rounded-xl shadow-lg p-6 border border-orange-200">
                            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                                <div>
                                    <h1 className="text-3xl font-bold text-gray-900 mb-2">
                                        Assign Categories to Products
                                    </h1>
                                    <p className="text-gray-700">
                                        Assign multiple categories to multiple products efficiently
                                    </p>
                                    <div className="flex flex-col gap-1 mt-2 text-sm">
                                        <div className="flex items-center text-orange-700">
                                            <FaArrowsAlt className="h-4 w-4 mr-1" />
                                            <span>Select products and categories, then move to assignment area</span>
                                        </div>
                                        <div className="flex items-center text-blue-700">
                                            <span className="text-xs">🔄 New categories will be added to existing ones (preserves current categories)</span>
                                        </div>
                                        <div className="flex items-center text-green-700">
                                            <span className="text-xs">📋 All items are sorted A-Z with serial numbers for easy verification</span>
                                        </div>
                                    </div>
                                </div>
                                <div className="flex flex-col sm:flex-row gap-2 items-start">
                                    <div className="flex flex-col sm:flex-row gap-2">
                                        <div className="bg-green-100 px-4 py-2 rounded-lg">
                                            <span className="text-sm font-medium text-green-800">
                                                📦 Products: {products.length} (A-Z sorted)
                                            </span>
                                        </div>
                                        <div className="bg-blue-100 px-4 py-2 rounded-lg">
                                            <span className="text-sm font-medium text-blue-800">
                                                🏷️ Categories: {categories.length} (A-Z sorted)
                                            </span>
                                        </div>
                                    </div>

                                    {/* Operation Mode Dropdown */}
                                    <div className="flex items-center gap-2">
                                        <label className="text-sm font-medium text-gray-700">Operation:</label>
                                        <select
                                            value={operationMode}
                                            onChange={(e) => setOperationMode(e.target.value)}
                                            className="border border-gray-300 rounded-lg px-3 py-2 bg-white text-black focus:ring-2 focus:ring-orange-500 focus:border-orange-500 text-sm"
                                        >
                                            <option value="assign">🔗 Assign Categories</option>
                                            <option value="deassign">🔓 De-assign Categories</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </motion.div>

                    {/* Main Content Grid */}
                    <div className={`grid grid-cols-1 gap-6 ${
                        operationMode === 'assign' ? 'lg:grid-cols-3' : 'lg:grid-cols-2'
                    }`}>
                        {/* Products Section */}
                        <motion.div
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            className="bg-white rounded-xl shadow-lg border border-orange-200"
                        >
                            <div className="p-6 border-b border-gray-200">
                                <div className="flex items-center justify-between mb-4">
                                    <h2 className="text-xl font-semibold text-gray-900 flex items-center">
                                        <ShoppingBagIcon className="h-6 w-6 text-orange-500 mr-2" />
                                        Products
                                    </h2>
                                    <span className="bg-orange-100 text-orange-800 text-sm font-medium px-2 py-1 rounded">
                                        {selectedProducts.length} selected
                                    </span>
                                </div>

                                {/* Search Products */}
                                <div className="relative mb-4">
                                    <MagnifyingGlassIcon className="h-5 w-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
                                    <input
                                        type="text"
                                        placeholder="Search products..."
                                        value={searchProduct}
                                        onChange={(e) => setSearchProduct(e.target.value)}
                                        className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 text-black"
                                    />
                                </div>

                                {/* Move to Targeted Products Button */}
                                {selectedProducts.length > 0 && (
                                    <button
                                        onClick={moveToTargetedProducts}
                                        className="w-full mb-4 px-4 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors flex items-center justify-center"
                                    >
                                        <PlusCircleIcon className="h-5 w-5 mr-2" />
                                        Add to Targeted Products ({selectedProducts.length})
                                    </button>
                                )}
                            </div>

                            {/* Products List */}
                            <div className="p-6 max-h-96 overflow-y-auto">
                                {filteredProducts.length === 0 ? (
                                    <div className="text-center py-8">
                                        <ShoppingBagIcon className="h-12 w-12 text-gray-500 mx-auto mb-2" />
                                        <p className="text-gray-700">No products found</p>
                                    </div>
                                ) : (
                                    <div className="space-y-2">
                                        {filteredProducts.map((product, index) => (
                                            <div
                                                key={product.id}
                                                onClick={() => handleProductSelect(product)}
                                                className={`p-3 border rounded-lg cursor-pointer transition-all ${
                                                    selectedProducts.find(p => p.id === product.id)
                                                        ? 'border-orange-500 bg-orange-50'
                                                        : 'border-gray-200 hover:border-orange-300 hover:bg-orange-50'
                                                }`}
                                            >
                                                <div className="flex items-center">
                                                    <div className="flex items-center mr-3">
                                                        <span className="text-xs font-medium text-gray-500 bg-gray-100 px-2 py-1 rounded mr-2">
                                                            #{index + 1}
                                                        </span>
                                                        <div className={`w-5 h-5 rounded border-2 flex items-center justify-center ${
                                                            selectedProducts.find(p => p.id === product.id)
                                                                ? 'border-orange-500 bg-orange-500'
                                                                : 'border-gray-300'
                                                        }`}>
                                                            {selectedProducts.find(p => p.id === product.id) && (
                                                                <CheckCircleIcon className="h-3 w-3 text-white" />
                                                            )}
                                                        </div>
                                                    </div>
                                                    <div className="flex-1">
                                                        <p className="font-medium text-gray-900 text-sm">{product.name}</p>
                                                        <p className="text-xs text-gray-600">SKU: {product.sku}</p>
                                                        <p className="text-xs text-orange-700 font-medium">${product.current_price}</p>
                                                    </div>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                )}
                            </div>
                        </motion.div>

                        {/* Categories Section - Only show for assign mode */}
                        {operationMode === 'assign' && (
                            <motion.div
                                initial={{ opacity: 0, x: 20 }}
                                animate={{ opacity: 1, x: 0 }}
                                className="bg-white rounded-xl shadow-lg border border-orange-200"
                            >
                            <div className="p-6 border-b border-gray-200">
                                <div className="flex items-center justify-between mb-4">
                                    <h2 className="text-xl font-semibold text-gray-900 flex items-center">
                                        <TagIcon className="h-6 w-6 text-orange-500 mr-2" />
                                        Categories
                                    </h2>
                                    <span className="bg-orange-100 text-orange-800 text-sm font-medium px-2 py-1 rounded">
                                        {selectedCategories.length} selected
                                    </span>
                                </div>

                                {/* Search Categories */}
                                <div className="relative mb-4">
                                    <MagnifyingGlassIcon className="h-5 w-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
                                    <input
                                        type="text"
                                        placeholder="Search categories..."
                                        value={searchCategory}
                                        onChange={(e) => setSearchCategory(e.target.value)}
                                        className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 text-black"
                                    />
                                </div>

                                {/* Move to Selected Categories Button */}
                                {selectedCategories.length > 0 && (
                                    <button
                                        onClick={moveToSelectedCategories}
                                        className="w-full mb-4 px-4 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors flex items-center justify-center"
                                    >
                                        <PlusCircleIcon className="h-5 w-5 mr-2" />
                                        Add to Selected Categories ({selectedCategories.length})
                                    </button>
                                )}
                            </div>

                            {/* Categories List */}
                            <div className="p-6 max-h-96 overflow-y-auto">
                                {filteredCategories.length === 0 ? (
                                    <div className="text-center py-8">
                                        <TagIcon className="h-12 w-12 text-gray-500 mx-auto mb-2" />
                                        <p className="text-gray-700">No categories found</p>
                                    </div>
                                ) : (
                                    <div className="space-y-2">
                                        {filteredCategories.map((category, index) => (
                                            <div
                                                key={category.id}
                                                onClick={() => handleCategorySelect(category)}
                                                className={`p-3 border rounded-lg cursor-pointer transition-all ${
                                                    selectedCategories.find(c => c.id === category.id)
                                                        ? 'border-orange-500 bg-orange-50'
                                                        : 'border-gray-200 hover:border-orange-300 hover:bg-orange-50'
                                                }`}
                                            >
                                                <div className="flex items-center">
                                                    <div className="flex items-center mr-3">
                                                        <span className="text-xs font-medium text-gray-500 bg-gray-100 px-2 py-1 rounded mr-2">
                                                            #{index + 1}
                                                        </span>
                                                        <div className={`w-5 h-5 rounded border-2 flex items-center justify-center ${
                                                            selectedCategories.find(c => c.id === category.id)
                                                                ? 'border-orange-500 bg-orange-500'
                                                                : 'border-gray-300'
                                                        }`}>
                                                            {selectedCategories.find(c => c.id === category.id) && (
                                                                <CheckCircleIcon className="h-3 w-3 text-white" />
                                                            )}
                                                        </div>
                                                    </div>
                                                    <div className="flex-1">
                                                        <p className="font-medium text-gray-900 text-sm">{category.name}</p>
                                                        <p className="text-xs text-gray-600">{category.description || 'No description'}</p>
                                                        <p className="text-xs text-orange-700 font-medium">{category.product_count} products</p>
                                                    </div>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                )}
                            </div>
                        </motion.div>
                        )}

                        {/* Assignment Area */}
                        <motion.div
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            className="bg-white rounded-xl shadow-lg border border-orange-200"
                        >
                            <div className="p-6 border-b border-gray-200">
                                <h2 className="text-xl font-semibold text-gray-900 flex items-center">
                                    <FaArrowsAlt className="h-6 w-6 text-orange-500 mr-2" />
                                    {operationMode === 'assign' ? 'Assignment Area' : 'De-assignment Area'}
                                </h2>
                                <p className="text-sm text-gray-700 mt-1">
                                    {operationMode === 'assign'
                                        ? 'Move products and categories here to assign'
                                        : 'Move products here to de-assign all categories (reset to default)'
                                    }
                                </p>
                            </div>

                            <div className="p-6 space-y-6">
                                {/* Targeted Products Box */}
                                <Droppable droppableId="targeted-products">
                                    {(provided, snapshot) => (
                                        <div
                                            ref={provided.innerRef}
                                            {...provided.droppableProps}
                                            className={`border-2 border-dashed rounded-lg p-4 min-h-32 transition-colors ${
                                                snapshot.isDraggingOver
                                                    ? 'border-orange-500 bg-orange-50'
                                                    : 'border-gray-300 bg-gray-50'
                                            }`}
                                        >
                                            <div className="flex items-center justify-between mb-3">
                                                <h3 className="font-medium text-gray-900 flex items-center">
                                                    <FaBoxes className="h-4 w-4 text-orange-500 mr-2" />
                                                    Targeted Products ({targetedProducts.length})
                                                </h3>
                                                {targetedProducts.length > 0 && (
                                                    <button
                                                        onClick={() => {
                                                            setTargetedProducts([]);
                                                            setProductCategories({});
                                                        }}
                                                        className="text-red-500 hover:text-red-700 text-sm"
                                                    >
                                                        Clear All
                                                    </button>
                                                )}
                                            </div>

                                            {targetedProducts.length === 0 ? (
                                                <div className="text-center py-4">
                                                    <ShoppingBagIcon className="h-8 w-8 text-gray-500 mx-auto mb-2" />
                                                    <p className="text-gray-700 text-sm">No products selected</p>
                                                    <p className="text-gray-600 text-xs">Select products from the left panel</p>
                                                </div>
                                            ) : (
                                                <div className="space-y-2 max-h-32 overflow-y-auto">
                                                    {targetedProducts.map((product) => (
                                                        <Draggable
                                                            key={`targeted-${product.id}`}
                                                            draggableId={`targeted-product-${product.id}`}
                                                            index={targetedProducts.indexOf(product)}
                                                        >
                                                            {(provided, snapshot) => (
                                                                <div
                                                                    ref={provided.innerRef}
                                                                    {...provided.draggableProps}
                                                                    {...provided.dragHandleProps}
                                                                    className={`p-2 bg-white border rounded-lg flex items-center justify-between transition-all ${
                                                                        snapshot.isDragging
                                                                            ? 'shadow-lg border-orange-300'
                                                                            : 'border-gray-200 hover:border-orange-300'
                                                                    }`}
                                                                >
                                                                    <div className="flex-1">
                                                                        <p className="text-sm font-medium text-gray-900">{product.name}</p>
                                                                        <p className="text-xs text-gray-600">SKU: {product.sku}</p>
                                                                        {/* Show existing categories */}
                                                                        {productCategories[product.id] && productCategories[product.id].length > 0 && (
                                                                            <div className="mt-1">
                                                                                <p className="text-xs text-gray-500 mb-1">Current categories:</p>
                                                                                <div className="flex flex-wrap gap-1">
                                                                                    {productCategories[product.id].map((category) => (
                                                                                        <span
                                                                                            key={category.id}
                                                                                            className="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800"
                                                                                        >
                                                                                            {category.name}
                                                                                        </span>
                                                                                    ))}
                                                                                </div>
                                                                            </div>
                                                                        )}
                                                                    </div>
                                                                    <button
                                                                        onClick={() => removeFromTargetedProducts(product.id)}
                                                                        className="text-red-500 hover:text-red-700 ml-2"
                                                                    >
                                                                        <XMarkIcon className="h-4 w-4" />
                                                                    </button>
                                                                </div>
                                                            )}
                                                        </Draggable>
                                                    ))}
                                                </div>
                                            )}
                                            {provided.placeholder}
                                        </div>
                                    )}
                                </Droppable>

                                {/* Selected Categories Box - Only show for assign mode */}
                                {operationMode === 'assign' && (
                                    <Droppable droppableId="selected-categories">
                                    {(provided, snapshot) => (
                                        <div
                                            ref={provided.innerRef}
                                            {...provided.droppableProps}
                                            className={`border-2 border-dashed rounded-lg p-4 min-h-32 transition-colors ${
                                                snapshot.isDraggingOver
                                                    ? 'border-orange-500 bg-orange-50'
                                                    : 'border-gray-300 bg-gray-50'
                                            }`}
                                        >
                                            <div className="flex items-center justify-between mb-3">
                                                <h3 className="font-medium text-gray-900 flex items-center">
                                                    <FaTags className="h-4 w-4 text-orange-500 mr-2" />
                                                    Selected Categories ({selectedCategoriesBox.length})
                                                </h3>
                                                {selectedCategoriesBox.length > 0 && (
                                                    <button
                                                        onClick={() => setSelectedCategoriesBox([])}
                                                        className="text-red-500 hover:text-red-700 text-sm"
                                                    >
                                                        Clear All
                                                    </button>
                                                )}
                                            </div>

                                            {selectedCategoriesBox.length === 0 ? (
                                                <div className="text-center py-4">
                                                    <TagIcon className="h-8 w-8 text-gray-500 mx-auto mb-2" />
                                                    <p className="text-gray-700 text-sm">No categories selected</p>
                                                    <p className="text-gray-600 text-xs">Select categories from the middle panel</p>
                                                </div>
                                            ) : (
                                                <div className="space-y-2 max-h-32 overflow-y-auto">
                                                    {selectedCategoriesBox.map((category) => (
                                                        <Draggable
                                                            key={`selected-${category.id}`}
                                                            draggableId={`selected-category-${category.id}`}
                                                            index={selectedCategoriesBox.indexOf(category)}
                                                        >
                                                            {(provided, snapshot) => (
                                                                <div
                                                                    ref={provided.innerRef}
                                                                    {...provided.draggableProps}
                                                                    {...provided.dragHandleProps}
                                                                    className={`p-2 bg-white border rounded-lg flex items-center justify-between transition-all ${
                                                                        snapshot.isDragging
                                                                            ? 'shadow-lg border-orange-300'
                                                                            : 'border-gray-200 hover:border-orange-300'
                                                                    }`}
                                                                >
                                                                    <div className="flex-1">
                                                                        <p className="text-sm font-medium text-gray-900">{category.name}</p>
                                                                        <p className="text-xs text-gray-600">{category.product_count} products</p>
                                                                    </div>
                                                                    <button
                                                                        onClick={() => removeFromSelectedCategoriesBox(category.id)}
                                                                        className="text-red-500 hover:text-red-700 ml-2"
                                                                    >
                                                                        <XMarkIcon className="h-4 w-4" />
                                                                    </button>
                                                                </div>
                                                            )}
                                                        </Draggable>
                                                    ))}
                                                </div>
                                            )}
                                            {provided.placeholder}
                                        </div>
                                    )}
                                </Droppable>
                                )}

                                {/* Assignment Zone */}
                                <Droppable droppableId="assignment-area">
                                    {(provided, snapshot) => (
                                        <div
                                            ref={provided.innerRef}
                                            {...provided.droppableProps}
                                            className={`border-2 border-dashed rounded-lg p-6 min-h-40 transition-all ${
                                                snapshot.isDraggingOver
                                                    ? 'border-orange-500 bg-orange-50 scale-105'
                                                    : 'border-orange-300 bg-orange-25'
                                            }`}
                                        >
                                            <div className="text-center">
                                                <FaArrowsAlt className={`h-12 w-12 mx-auto mb-4 transition-colors ${
                                                    snapshot.isDraggingOver ? 'text-orange-600' : 'text-orange-400'
                                                }`} />
                                                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                                                    {operationMode === 'assign' ? 'Assignment Area' : 'De-assignment Area'}
                                                </h3>
                                                <p className="text-gray-700 mb-4">
                                                    {operationMode === 'assign'
                                                        ? 'Move products and categories here to assign categories to products'
                                                        : 'Move products here to de-assign all categories (reset to default General Product Category)'
                                                    }
                                                </p>

                                                {/* Status */}
                                                <div className="space-y-2">
                                                    <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm ${
                                                        targetedProducts.length > 0
                                                            ? 'bg-green-100 text-green-800'
                                                            : 'bg-gray-100 text-gray-700'
                                                    }`}>
                                                        <CheckCircleIcon className="h-4 w-4 mr-1" />
                                                        Products: {targetedProducts.length}
                                                    </div>
                                                    {operationMode === 'assign' && (
                                                        <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm ml-2 ${
                                                            selectedCategoriesBox.length > 0
                                                                ? 'bg-green-100 text-green-800'
                                                                : 'bg-gray-100 text-gray-700'
                                                        }`}>
                                                            <CheckCircleIcon className="h-4 w-4 mr-1" />
                                                            Categories: {selectedCategoriesBox.length}
                                                        </div>
                                                    )}
                                                </div>

                                                {/* Assignment/De-assignment Button */}
                                                {operationMode === 'assign' ? (
                                                    targetedProducts.length > 0 && selectedCategoriesBox.length > 0 && (
                                                        <button
                                                            onClick={() => onDragEnd({ destination: { droppableId: 'assignment-area' } })}
                                                            className="mt-4 px-6 py-3 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors font-medium"
                                                        >
                                                            🔗 Assign Categories to Products
                                                        </button>
                                                    )
                                                ) : (
                                                    targetedProducts.length > 0 && (
                                                        <button
                                                            onClick={() => onDragEnd({ destination: { droppableId: 'assignment-area' } })}
                                                            className="mt-4 px-6 py-3 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors font-medium"
                                                        >
                                                            🔓 De-assign Categories from Products
                                                        </button>
                                                    )
                                                )}
                                            </div>
                                            {provided.placeholder}
                                        </div>
                                    )}
                                </Droppable>
                            </div>
                        </motion.div>
                    </div>

                    {/* Assignment History */}
                    {assignmentHistory.length > 0 && (
                        <motion.div
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            className="mt-8 bg-white rounded-xl shadow-lg border border-orange-200"
                        >
                            <div className="p-6 border-b border-gray-200">
                                <h2 className="text-xl font-semibold text-gray-900">Assignment History</h2>
                            </div>
                            <div className="p-6">
                                <div className="space-y-3">
                                    {assignmentHistory.slice(-5).reverse().map((assignment, index) => (
                                        <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                            <div className="flex items-center">
                                                <CheckCircleIcon className="h-5 w-5 text-green-500 mr-3" />
                                                <span className="text-sm text-gray-900">
                                                    Assigned {assignment.categories} categories to {assignment.products} products
                                                </span>
                                            </div>
                                            <span className="text-xs text-gray-600">
                                                {assignment.timestamp.toLocaleString()}
                                            </span>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        </motion.div>
                    )}

                    {/* Product Categories Display */}
                    <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="mt-8 bg-white rounded-xl shadow-lg border border-orange-200"
                    >
                        <div className="p-6 border-b border-gray-200">
                            <h2 className="text-xl font-semibold text-gray-900">Product Categories Overview</h2>
                            <p className="text-sm text-gray-700 mt-1">View which categories are assigned to each product</p>
                        </div>
                        <div className="p-6">
                            {products.length === 0 ? (
                                <div className="text-center py-8">
                                    <ShoppingBagIcon className="h-12 w-12 text-gray-500 mx-auto mb-2" />
                                    <p className="text-gray-700">No products available</p>
                                </div>
                            ) : (
                                <div className="space-y-4 max-h-96 overflow-y-auto">
                                    {products.filter(product =>
                                        product.name.toLowerCase().includes(searchProduct.toLowerCase()) ||
                                        product.sku.toLowerCase().includes(searchProduct.toLowerCase())
                                    ).map((product) => (
                                        <div key={product.id} className="p-4 border border-gray-200 rounded-lg hover:border-orange-300 transition-colors">
                                            <div className="flex items-start justify-between">
                                                <div className="flex-1">
                                                    <h3 className="font-medium text-gray-900">{product.name}</h3>
                                                    <p className="text-sm text-gray-600">SKU: {product.sku}</p>
                                                    <p className="text-sm text-orange-700 font-medium">${product.current_price}</p>
                                                </div>
                                                <div className="ml-4">
                                                    <p className="text-xs text-gray-600 mb-2">Categories:</p>
                                                    {product.categories && product.categories.length > 0 ? (
                                                        <div className="flex flex-wrap gap-1">
                                                            {product.categories.map((category) => (
                                                                <span
                                                                    key={category.id}
                                                                    className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800"
                                                                >
                                                                    {category.name}
                                                                </span>
                                                            ))}
                                                        </div>
                                                    ) : (
                                                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-600">
                                                            No categories assigned
                                                        </span>
                                                    )}
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            )}
                        </div>
                    </motion.div>
                </div>
            </div>
        </DragDropContext>
    );
}