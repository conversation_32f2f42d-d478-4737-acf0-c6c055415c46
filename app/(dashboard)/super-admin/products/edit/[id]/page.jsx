'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Swal from 'sweetalert2';

export default function EditProductPage() {
    const params = useParams();
    const router = useRouter();
    const productId = params.id;

    // State management
    const [product, setProduct] = useState(null);
    const [loading, setLoading] = useState(true);
    const [saving, setSaving] = useState(false);
    const [activeTab, setActiveTab] = useState('basic');


    // Form data state
    const [basicInfo, setBasicInfo] = useState({
        name: '',
        title: '',
        description: '',
        sku: '',
        quantity: 0,
        is_active: true,
        is_featured: false,
        meta_title: '',
        meta_description: '',
        meta_keywords: ''
    });

    const [pricingInfo, setPricingInfo] = useState({
        pack_price: {
            number_of_products: 1,
            per_pack_price: 0,
            per_pack_special_price: 0,
            customer_margin: 15,
            partner_margin: 8,
            customer_margin_type: 'percentage',
            partner_margin_type: 'percentage',
            delivery_fee: 0
        },
        bulk_prices: []
    });

    // Fetch product data on component mount
    useEffect(() => {
        if (productId) {
            fetchProductData();
        }
    }, [productId]);

    // Debug: Watch pricing info state changes
    useEffect(() => {
        console.log('🔄 PRICING INFO STATE CHANGED:');
        console.log('- Current pricingInfo:', pricingInfo);
        console.log('- Bulk prices in state:', pricingInfo.bulk_prices);
        console.log('- Bulk prices count in state:', pricingInfo.bulk_prices?.length || 0);
    }, [pricingInfo]);

    const fetchProductData = async () => {
        try {
            setLoading(true);
            const token = localStorage.getItem('superAdminAuthToken');

            if (!token) {
                Swal.fire({
                    icon: 'error',
                    title: 'Authentication Error',
                    text: 'Please login again',
                });
                router.push('/login/super-admin');
                return;
            }

            const response = await fetch(`https://b2b.instinctfusionx.xyz/public/api/v1/admin/products/${productId}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();

            console.log('🌐 FULL API RESPONSE:', JSON.stringify(data, null, 2));

            if (data.success && data.data && data.data.product) {
                const productData = data.data.product;
                setProduct(productData);

                console.log('🔍 PRODUCT DATA STRUCTURE:');
                console.log('- Product ID:', productData.id);
                console.log('- Product Name:', productData.name);
                console.log('- Pack Price Object:', productData.pack_price);
                console.log('- Bulk Prices Type:', typeof productData.bulk_prices);
                console.log('- Bulk Prices Array.isArray:', Array.isArray(productData.bulk_prices));
                console.log('- Bulk Prices Length:', productData.bulk_prices?.length);
                console.log('- Bulk Prices Raw Data:', productData.bulk_prices);
                console.log('- Bulk Prices JSON:', JSON.stringify(productData.bulk_prices, null, 2));

                // Populate basic info
                setBasicInfo({
                    name: productData.name || '',
                    title: productData.title || '',
                    description: productData.description || '',
                    sku: productData.sku || '',
                    quantity: productData.quantity || 0,
                    is_active: productData.is_active || false,
                    is_featured: productData.is_featured || false,
                    meta_title: productData.meta_title || '',
                    meta_description: productData.meta_description || '',
                    meta_keywords: productData.meta_keywords || ''
                });

                // Handle bulk pricing data properly - SMART ARRAY PROCESSING
                const bulkPrices = productData.bulk_prices;

                console.log('� SMART BULK PRICING ANALYSIS STARTING...');
                console.log('📊 Raw productData.bulk_prices:', bulkPrices);
                console.log('📊 Type of bulk_prices:', typeof bulkPrices);
                console.log('📊 Is Array:', Array.isArray(bulkPrices));
                console.log('📊 Truthiness check:', !!bulkPrices);
                console.log('📊 Length property:', bulkPrices?.length);

                // SMART ARRAY ITERATION - Loop through and check what exists
                let processedBulkPrices = [];

                console.log('🔄 STARTING SMART ARRAY ITERATION...');

                // Check if we have any bulk pricing data at all
                if (bulkPrices) {
                    console.log('✅ bulkPrices exists, checking structure...');

                    if (Array.isArray(bulkPrices)) {
                        console.log(`✅ bulkPrices is an array with ${bulkPrices.length} items`);

                        // LOOP THROUGH EACH ITEM AND EXAMINE IT
                        for (let i = 0; i < bulkPrices.length; i++) {
                            const bulkItem = bulkPrices[i];
                            console.log(`\n� EXAMINING BULK ITEM ${i + 1}:`);
                            console.log('- Full item:', bulkItem);
                            console.log('- Item type:', typeof bulkItem);
                            console.log('- Item keys:', Object.keys(bulkItem || {}));

                            // Check each expected field
                            console.log('- id field:', bulkItem?.id);
                            console.log('- number_of_packs field:', bulkItem?.number_of_packs);
                            console.log('- per_pack_price field:', bulkItem?.per_pack_price);
                            console.log('- per_pack_special_price field:', bulkItem?.per_pack_special_price);
                            console.log('- customer_margin field:', bulkItem?.customer_margin);
                            console.log('- partner_margin field:', bulkItem?.partner_margin);
                            console.log('- delivery_fee field:', bulkItem?.delivery_fee);
                            console.log('- customer_margin_type field:', bulkItem?.customer_margin_type);
                            console.log('- partner_margin_type field:', bulkItem?.partner_margin_type);

                            // Check if this item has the minimum required fields
                            if (bulkItem && (bulkItem.number_of_packs || bulkItem.per_pack_price)) {
                                console.log(`✅ Item ${i + 1} has valid bulk pricing data, processing...`);

                                const processed = {
                                    id: bulkItem.id || null,
                                    number_of_packs: parseInt(bulkItem.number_of_packs) || 0,
                                    per_pack_price: parseFloat(bulkItem.per_pack_price) || 0,
                                    per_pack_special_price: parseFloat(bulkItem.per_pack_special_price) || 0,
                                    customer_margin: parseFloat(bulkItem.customer_margin) || 15,
                                    partner_margin: parseFloat(bulkItem.partner_margin) || 8,
                                    customer_margin_type: bulkItem.customer_margin_type || 'percentage',
                                    partner_margin_type: bulkItem.partner_margin_type || 'percentage',
                                    delivery_fee: parseFloat(bulkItem.delivery_fee) || 0
                                };

                                console.log(`✅ Processed item ${i + 1}:`, processed);
                                processedBulkPrices.push(processed);
                            } else {
                                console.log(`⚠️ Item ${i + 1} does not have valid bulk pricing data, skipping...`);
                            }
                        }

                        console.log(`\n🎯 ARRAY ITERATION COMPLETE: Found ${processedBulkPrices.length} valid bulk pricing items`);

                    } else {
                        console.log('❌ bulkPrices is not an array, checking if it\'s an object...');
                        console.log('- bulkPrices object keys:', Object.keys(bulkPrices));
                        console.log('- bulkPrices object values:', Object.values(bulkPrices));

                        // Maybe it's a single object instead of array?
                        if (typeof bulkPrices === 'object' && (bulkPrices.number_of_packs || bulkPrices.per_pack_price)) {
                            console.log('✅ Found single bulk pricing object, converting to array...');
                            processedBulkPrices = [{
                                id: bulkPrices.id || null,
                                number_of_packs: parseInt(bulkPrices.number_of_packs) || 0,
                                per_pack_price: parseFloat(bulkPrices.per_pack_price) || 0,
                                per_pack_special_price: parseFloat(bulkPrices.per_pack_special_price) || 0,
                                customer_margin: parseFloat(bulkPrices.customer_margin) || 15,
                                partner_margin: parseFloat(bulkPrices.partner_margin) || 8,
                                customer_margin_type: bulkPrices.customer_margin_type || 'percentage',
                                partner_margin_type: bulkPrices.partner_margin_type || 'percentage',
                                delivery_fee: parseFloat(bulkPrices.delivery_fee) || 0
                            }];
                        }
                    }
                } else {
                    console.log('❌ No bulk pricing data found - bulkPrices is null/undefined');
                }

                console.log('\n🏁 FINAL BULK PRICING PROCESSING RESULTS:');
                console.log('- Total processed items:', processedBulkPrices.length);
                console.log('- Processed bulk pricing array:', processedBulkPrices);
                console.log('- Each item details:');
                processedBulkPrices.forEach((item, index) => {
                    console.log(`  Item ${index + 1}:`, item);
                });

                const finalPricingInfo = {
                    pack_price: {
                        number_of_products: productData.pack_price?.number_of_products || 1,
                        per_pack_price: parseFloat(productData.pack_price?.per_pack_price || 0),
                        per_pack_special_price: parseFloat(productData.pack_price?.per_pack_special_price || 0),
                        customer_margin: parseFloat(productData.pack_price?.customer_margin || 15),
                        partner_margin: parseFloat(productData.pack_price?.partner_margin || 8),
                        customer_margin_type: productData.pack_price?.customer_margin_type || 'percentage',
                        partner_margin_type: productData.pack_price?.partner_margin_type || 'percentage',
                        delivery_fee: parseFloat(productData.pack_price?.delivery_fee || 0)
                    },
                    bulk_prices: processedBulkPrices
                };

                console.log('🎯 SETTING PRICING INFO STATE:');
                console.log('- Pack price data:', finalPricingInfo.pack_price);
                console.log('- Bulk prices data:', finalPricingInfo.bulk_prices);
                console.log('- Bulk prices count:', finalPricingInfo.bulk_prices.length);

                setPricingInfo(finalPricingInfo);

                console.log('✅ Product data loaded successfully');
                console.log('📊 Final pricing info set in state:', finalPricingInfo);
            } else {
                throw new Error('Invalid product data structure');
            }
        } catch (error) {
            console.error('❌ Error fetching product:', error);
            Swal.fire({
                icon: 'error',
                title: 'Error Loading Product',
                text: `Failed to load product data: ${error.message}`,
            });
        } finally {
            setLoading(false);
        }
    };

    // Update basic product attributes using PATCH API
    const updateBasicInfo = async (updatedBasicInfo) => {
        try {
            setSaving(true);
            const token = localStorage.getItem('superAdminAuthToken');

            if (!token) {
                throw new Error('No authentication token found');
            }

            // Filter out empty/null values and prepare valid attributes
            const validAttributes = [];
            const validValues = [];

            // Only include fields that have values and are valid for the API
            Object.entries(updatedBasicInfo).forEach(([key, value]) => {
                // Skip null, undefined, or empty string values for optional fields
                if (value !== null && value !== undefined && value !== '') {
                    validAttributes.push(key);
                    validValues.push(value);
                } else if (key === 'is_active' || key === 'is_featured') {
                    // Always include boolean fields even if false
                    validAttributes.push(key);
                    validValues.push(value);
                }
            });

            const payload = {
                attributes: validAttributes,
                values: validValues
            };

            console.log('🔄 Updating basic info:', payload);

            const response = await fetch(`https://b2b.instinctfusionx.xyz/public/api/v1/admin/products/${productId}/update-attributes`, {
                method: 'PATCH',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                },
                body: JSON.stringify(payload)
            });

            const data = await response.json();
            console.log('📥 API Response:', data);

            if (!response.ok) {
                // Get more detailed error information
                const errorMessage = data.message || data.error || `HTTP error! status: ${response.status}`;
                const validationErrors = data.errors ? JSON.stringify(data.errors) : '';
                throw new Error(`${errorMessage}${validationErrors ? ` - Validation errors: ${validationErrors}` : ''}`);
            }

            if (data.success) {
                console.log('✅ Basic info updated successfully');
                return true;
            } else {
                throw new Error(data.message || 'Failed to update basic info');
            }
        } catch (error) {
            console.error('❌ Error updating basic info:', error);
            throw error;
        } finally {
            setSaving(false);
        }
    };

    // Update pricing information using PUT API
    const updatePricingInfo = async (updatedPricingInfo) => {
        try {
            setSaving(true);
            const token = localStorage.getItem('superAdminAuthToken');

            if (!token) {
                throw new Error('No authentication token found');
            }

            console.log('🔄 Updating pricing info:', updatedPricingInfo);

            const response = await fetch(`https://b2b.instinctfusionx.xyz/public/api/v1/admin/products/${productId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                },
                body: JSON.stringify(updatedPricingInfo)
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();

            if (data.success) {
                console.log('✅ Pricing info updated successfully');
                return true;
            } else {
                throw new Error(data.message || 'Failed to update pricing info');
            }
        } catch (error) {
            console.error('❌ Error updating pricing info:', error);
            throw error;
        } finally {
            setSaving(false);
        }
    };

    // Handle basic info form submission
    const handleBasicInfoSubmit = async (e) => {
        e.preventDefault();

        // Basic validation
        if (!basicInfo.name || !basicInfo.sku || !basicInfo.description) {
            Swal.fire({
                icon: 'warning',
                title: 'Validation Error',
                text: 'Please fill in all required fields: Product Name, SKU, and Description.',
                confirmButtonColor: '#f97316'
            });
            return;
        }

        try {
            await updateBasicInfo(basicInfo);

            Swal.fire({
                icon: 'success',
                title: 'Success!',
                text: 'Basic information updated successfully',
                confirmButtonColor: '#f97316'
            });

            // Refresh product data
            await fetchProductData();
        } catch (error) {
            console.error('❌ Basic info update failed:', error);

            Swal.fire({
                icon: 'error',
                title: 'Update Failed!',
                html: `
                    <div class="text-left">
                        <p><strong>Error:</strong> ${error.message}</p>
                        <br>
                        <p class="text-sm text-gray-600">
                            Please check the console for more details or try again with different values.
                        </p>
                    </div>
                `,
                confirmButtonColor: '#f97316',
                width: '500px'
            });
        }
    };

    // Handle pricing info form submission
    const handlePricingInfoSubmit = async (e) => {
        e.preventDefault();

        try {
            await updatePricingInfo(pricingInfo);

            Swal.fire({
                icon: 'success',
                title: 'Success!',
                text: 'Pricing information updated successfully',
                confirmButtonColor: '#f97316'
            });

            // Refresh product data
            await fetchProductData();
        } catch (error) {
            Swal.fire({
                icon: 'error',
                title: 'Error!',
                text: `Failed to update pricing information: ${error.message}`,
                confirmButtonColor: '#f97316'
            });
        }
    };

    // Add new bulk price tier
    const addBulkPrice = () => {
        const newBulkPrice = {
            number_of_packs: 0,
            per_pack_price: 0,
            per_pack_special_price: 0,
            customer_margin: 15,
            partner_margin: 8,
            customer_margin_type: 'percentage',
            partner_margin_type: 'percentage',
            delivery_fee: 0
        };

        setPricingInfo(prev => ({
            ...prev,
            bulk_prices: [...prev.bulk_prices, newBulkPrice]
        }));
    };

    // Remove bulk price tier
    const removeBulkPrice = (index) => {
        setPricingInfo(prev => ({
            ...prev,
            bulk_prices: prev.bulk_prices.filter((_, i) => i !== index)
        }));
    };

    // Update bulk price tier
    const updateBulkPrice = (index, field, value) => {
        setPricingInfo(prev => ({
            ...prev,
            bulk_prices: prev.bulk_prices.map((bulk, i) =>
                i === index ? { ...bulk, [field]: value } : bulk
            )
        }));
    };

    // Handle input changes for basic info
    const handleBasicInfoChange = (field, value) => {
        setBasicInfo(prev => ({
            ...prev,
            [field]: value
        }));
    };

    // Handle input changes for pack pricing
    const handlePackPriceChange = (field, value) => {
        setPricingInfo(prev => ({
            ...prev,
            pack_price: {
                ...prev.pack_price,
                [field]: value
            }
        }));
    };

    // Loading state
    if (loading) {
        return (
            <div className="p-6 bg-gray-100 min-h-screen">
                <div className="flex items-center justify-center h-64">
                    <div className="text-center">
                        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500 mx-auto mb-4"></div>
                        <p className="text-black">Loading product data...</p>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="p-6 bg-gray-100 min-h-screen">
            <div className="max-w-6xl mx-auto">
                {/* Header */}
                <div className="mb-6">
                    <div className="flex items-center justify-between">
                        <div>
                            <h1 className="text-2xl font-semibold text-black">Edit Product</h1>
                            <p className="text-black mt-1">
                                Product ID: {productId} | {product?.name}
                            </p>
                        </div>
                        <button
                            onClick={() => router.push('/super-admin/products/manage')}
                            className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
                        >
                            Back to Products
                        </button>
                    </div>
                </div>

                {/* Tab Navigation */}
                <div className="mb-6">
                    <div className="border-b border-gray-200">
                        <nav className="-mb-px flex space-x-8">
                            <button
                                onClick={() => setActiveTab('basic')}
                                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                                    activeTab === 'basic'
                                        ? 'border-orange-500 text-orange-600'
                                        : 'border-transparent text-black hover:text-orange-600 hover:border-gray-300'
                                }`}
                            >
                                Basic Information
                            </button>
                            <button
                                onClick={() => setActiveTab('pricing')}
                                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                                    activeTab === 'pricing'
                                        ? 'border-orange-500 text-orange-600'
                                        : 'border-transparent text-black hover:text-orange-600 hover:border-gray-300'
                                }`}
                            >
                                Pricing & Bulk Orders
                            </button>
                        </nav>
                    </div>
                </div>

                {/* Tab Content */}
                {activeTab === 'basic' && (
                    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                        <div className="p-6">
                            <h3 className="text-lg font-medium mb-6 text-black">Basic Product Information</h3>

                            <form onSubmit={handleBasicInfoSubmit} className="space-y-6">
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    {/* Product Name */}
                                    <div>
                                        <label className="block text-sm font-medium text-black mb-2">
                                            Product Name *
                                        </label>
                                        <input
                                            type="text"
                                            value={basicInfo.name}
                                            onChange={(e) => handleBasicInfoChange('name', e.target.value)}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 text-black"
                                            placeholder="Enter product name"
                                            required
                                        />
                                    </div>

                                    {/* Product Title */}
                                    <div>
                                        <label className="block text-sm font-medium text-black mb-2">
                                            Product Title
                                        </label>
                                        <input
                                            type="text"
                                            value={basicInfo.title}
                                            onChange={(e) => handleBasicInfoChange('title', e.target.value)}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 text-black"
                                            placeholder="Enter product title"
                                        />
                                    </div>

                                    {/* SKU */}
                                    <div>
                                        <label className="block text-sm font-medium text-black mb-2">
                                            SKU *
                                        </label>
                                        <input
                                            type="text"
                                            value={basicInfo.sku}
                                            onChange={(e) => handleBasicInfoChange('sku', e.target.value)}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 text-black"
                                            placeholder="Enter product SKU"
                                            required
                                        />
                                    </div>

                                    {/* Quantity */}
                                    <div>
                                        <label className="block text-sm font-medium text-black mb-2">
                                            Stock Quantity *
                                        </label>
                                        <input
                                            type="number"
                                            value={basicInfo.quantity}
                                            onChange={(e) => handleBasicInfoChange('quantity', parseInt(e.target.value) || 0)}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 text-black"
                                            placeholder="Enter stock quantity"
                                            min="0"
                                            required
                                        />
                                    </div>
                                </div>

                                {/* Description */}
                                <div>
                                    <label className="block text-sm font-medium text-black mb-2">
                                        Product Description *
                                    </label>
                                    <textarea
                                        value={basicInfo.description}
                                        onChange={(e) => handleBasicInfoChange('description', e.target.value)}
                                        rows={4}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 text-black"
                                        placeholder="Enter product description"
                                        required
                                    />
                                </div>

                                {/* Status Toggles */}
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div className="flex items-center">
                                        <input
                                            type="checkbox"
                                            id="is_active"
                                            checked={basicInfo.is_active}
                                            onChange={(e) => handleBasicInfoChange('is_active', e.target.checked)}
                                            className="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded"
                                        />
                                        <label htmlFor="is_active" className="ml-2 block text-sm text-black">
                                            Product is Active
                                        </label>
                                    </div>

                                    <div className="flex items-center">
                                        <input
                                            type="checkbox"
                                            id="is_featured"
                                            checked={basicInfo.is_featured}
                                            onChange={(e) => handleBasicInfoChange('is_featured', e.target.checked)}
                                            className="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded"
                                        />
                                        <label htmlFor="is_featured" className="ml-2 block text-sm text-black">
                                            Featured Product
                                        </label>
                                    </div>
                                </div>

                                {/* SEO Information */}
                                <div className="border-t pt-6">
                                    <h4 className="text-md font-medium text-black mb-4">SEO Information</h4>

                                    <div className="space-y-4">
                                        {/* Meta Title */}
                                        <div>
                                            <label className="block text-sm font-medium text-black mb-2">
                                                Meta Title
                                            </label>
                                            <input
                                                type="text"
                                                value={basicInfo.meta_title}
                                                onChange={(e) => handleBasicInfoChange('meta_title', e.target.value)}
                                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 text-black"
                                                placeholder="Enter meta title"
                                            />
                                        </div>

                                        {/* Meta Description */}
                                        <div>
                                            <label className="block text-sm font-medium text-black mb-2">
                                                Meta Description
                                            </label>
                                            <textarea
                                                value={basicInfo.meta_description}
                                                onChange={(e) => handleBasicInfoChange('meta_description', e.target.value)}
                                                rows={3}
                                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 text-black"
                                                placeholder="Enter meta description"
                                            />
                                        </div>

                                        {/* Meta Keywords */}
                                        <div>
                                            <label className="block text-sm font-medium text-black mb-2">
                                                Meta Keywords
                                            </label>
                                            <input
                                                type="text"
                                                value={basicInfo.meta_keywords}
                                                onChange={(e) => handleBasicInfoChange('meta_keywords', e.target.value)}
                                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 text-black"
                                                placeholder="Enter meta keywords (comma separated)"
                                            />
                                        </div>
                                    </div>
                                </div>

                                {/* Submit Button */}
                                <div className="flex justify-end pt-6 border-t">
                                    <button
                                        type="submit"
                                        disabled={saving}
                                        className="px-6 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
                                    >
                                        {saving ? (
                                            <div className="flex items-center">
                                                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                                                Updating...
                                            </div>
                                        ) : (
                                            'Update Basic Information'
                                        )}
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                )}

                {/* Pricing Tab */}
                {activeTab === 'pricing' && (
                    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                        <div className="p-6">
                            <h3 className="text-lg font-medium mb-6 text-black">Pricing & Bulk Orders</h3>

                            {/* Debug info - remove in production */}
                            <div className="mb-6 p-4 bg-blue-50 rounded-lg">
                                <p className="text-sm text-blue-800 mb-2">
                                    📊 Loaded pricing data: Pack price: ${pricingInfo.pack_price.per_pack_price} |
                                    Bulk tiers: {pricingInfo.bulk_prices.length}
                                </p>
                                {pricingInfo.bulk_prices.length > 0 && (
                                    <div className="text-xs text-blue-700">
                                        <p className="font-medium">Bulk Pricing Tiers:</p>
                                        {pricingInfo.bulk_prices.map((bulk, index) => (
                                            <p key={index} className="ml-2">
                                                • Tier {index + 1}: {bulk.number_of_packs}+ packs at ${bulk.per_pack_price} each
                                                {bulk.id && <span className="text-blue-500"> (ID: {bulk.id})</span>}
                                            </p>
                                        ))}
                                    </div>
                                )}
                                {pricingInfo.bulk_prices.length === 0 && (
                                    <p className="text-xs text-blue-600">No bulk pricing tiers found in API response</p>
                                )}
                            </div>

                            <form onSubmit={handlePricingInfoSubmit} className="space-y-8">
                                {/* Pack Pricing */}
                                <div>
                                    <h4 className="text-md font-medium text-black mb-4">Pack Pricing</h4>

                                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                        {/* Number of Products */}
                                        <div>
                                            <label className="block text-sm font-medium text-black mb-2">
                                                Products per Pack
                                            </label>
                                            <input
                                                type="number"
                                                value={pricingInfo.pack_price.number_of_products}
                                                onChange={(e) => handlePackPriceChange('number_of_products', parseInt(e.target.value) || 1)}
                                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 text-black"
                                                min="1"
                                                required
                                            />
                                        </div>

                                        {/* Per Pack Price */}
                                        <div>
                                            <label className="block text-sm font-medium text-black mb-2">
                                                Per Pack Price ($)
                                            </label>
                                            <input
                                                type="number"
                                                step="0.01"
                                                value={pricingInfo.pack_price.per_pack_price}
                                                onChange={(e) => handlePackPriceChange('per_pack_price', parseFloat(e.target.value) || 0)}
                                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 text-black"
                                                min="0"
                                                required
                                            />
                                        </div>

                                        {/* Per Pack Special Price */}
                                        <div>
                                            <label className="block text-sm font-medium text-black mb-2">
                                                Special Price ($)
                                            </label>
                                            <input
                                                type="number"
                                                step="0.01"
                                                value={pricingInfo.pack_price.per_pack_special_price}
                                                onChange={(e) => handlePackPriceChange('per_pack_special_price', parseFloat(e.target.value) || 0)}
                                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 text-black"
                                                min="0"
                                            />
                                        </div>

                                        {/* Customer Margin */}
                                        <div>
                                            <label className="block text-sm font-medium text-black mb-2">
                                                Customer Margin (%)
                                            </label>
                                            <input
                                                type="number"
                                                step="0.01"
                                                value={pricingInfo.pack_price.customer_margin}
                                                onChange={(e) => handlePackPriceChange('customer_margin', parseFloat(e.target.value) || 0)}
                                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 text-black"
                                                min="0"
                                            />
                                        </div>

                                        {/* Partner Margin */}
                                        <div>
                                            <label className="block text-sm font-medium text-black mb-2">
                                                Partner Margin (%)
                                            </label>
                                            <input
                                                type="number"
                                                step="0.01"
                                                value={pricingInfo.pack_price.partner_margin}
                                                onChange={(e) => handlePackPriceChange('partner_margin', parseFloat(e.target.value) || 0)}
                                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 text-black"
                                                min="0"
                                            />
                                        </div>

                                        {/* Delivery Fee */}
                                        <div>
                                            <label className="block text-sm font-medium text-black mb-2">
                                                Delivery Fee ($)
                                            </label>
                                            <input
                                                type="number"
                                                step="0.01"
                                                value={pricingInfo.pack_price.delivery_fee}
                                                onChange={(e) => handlePackPriceChange('delivery_fee', parseFloat(e.target.value) || 0)}
                                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 text-black"
                                                min="0"
                                            />
                                        </div>
                                    </div>
                                </div>

                                {/* Bulk Pricing */}
                                <div className="border-t pt-6">
                                    <div className="flex items-center justify-between mb-4">
                                        <div>
                                            <h4 className="text-md font-medium text-black">Bulk Pricing Tiers</h4>
                                            <p className="text-sm text-gray-600 mt-1">
                                                {pricingInfo.bulk_prices.length > 0
                                                    ? `${pricingInfo.bulk_prices.length} bulk pricing tier${pricingInfo.bulk_prices.length > 1 ? 's' : ''} configured`
                                                    : 'No bulk pricing configured - add tiers for volume discounts'
                                                }
                                            </p>
                                        </div>
                                        <button
                                            type="button"
                                            onClick={addBulkPrice}
                                            className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 text-sm"
                                        >
                                            Add Bulk Tier
                                        </button>
                                    </div>

                                    {pricingInfo.bulk_prices.length === 0 ? (
                                        <div className="text-center py-8 text-black">
                                            <p>No bulk pricing tiers configured.</p>
                                            <p className="text-sm">Click "Add Bulk Tier" to create volume discounts.</p>
                                        </div>
                                    ) : (
                                        <div className="space-y-4">
                                            {pricingInfo.bulk_prices.map((bulk, index) => (
                                                <div key={index} className="border border-gray-200 rounded-lg p-4 bg-gray-50">
                                                    <div className="flex items-center justify-between mb-4">
                                                        <div>
                                                            <h5 className="font-medium text-black">Tier {index + 1}</h5>
                                                            <p className="text-sm text-orange-600">
                                                                {bulk.number_of_packs > 0
                                                                    ? `${bulk.number_of_packs}+ packs at $${bulk.per_pack_price || 0} each`
                                                                    : 'Configure pack quantity and pricing'
                                                                }
                                                            </p>
                                                        </div>
                                                        <button
                                                            type="button"
                                                            onClick={() => removeBulkPrice(index)}
                                                            className="text-red-600 hover:text-red-800 text-sm px-3 py-1 rounded border border-red-300 hover:bg-red-50"
                                                        >
                                                            Remove Tier
                                                        </button>
                                                    </div>

                                                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                                        {/* Number of Packs */}
                                                        <div>
                                                            <label className="block text-sm font-medium text-black mb-1">
                                                                Number of Packs
                                                            </label>
                                                            <input
                                                                type="number"
                                                                value={bulk.number_of_packs}
                                                                onChange={(e) => updateBulkPrice(index, 'number_of_packs', parseInt(e.target.value) || 0)}
                                                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 text-black"
                                                                min="1"
                                                            />
                                                        </div>

                                                        {/* Per Pack Price */}
                                                        <div>
                                                            <label className="block text-sm font-medium text-black mb-1">
                                                                Per Pack Price ($)
                                                            </label>
                                                            <input
                                                                type="number"
                                                                step="0.01"
                                                                value={bulk.per_pack_price}
                                                                onChange={(e) => updateBulkPrice(index, 'per_pack_price', parseFloat(e.target.value) || 0)}
                                                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 text-black"
                                                                min="0"
                                                            />
                                                        </div>

                                                        {/* Special Price */}
                                                        <div>
                                                            <label className="block text-sm font-medium text-black mb-1">
                                                                Special Price ($)
                                                            </label>
                                                            <input
                                                                type="number"
                                                                step="0.01"
                                                                value={bulk.per_pack_special_price}
                                                                onChange={(e) => updateBulkPrice(index, 'per_pack_special_price', parseFloat(e.target.value) || 0)}
                                                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 text-black"
                                                                min="0"
                                                            />
                                                        </div>

                                                        {/* Customer Margin */}
                                                        <div>
                                                            <label className="block text-sm font-medium text-black mb-1">
                                                                Customer Margin (%)
                                                            </label>
                                                            <input
                                                                type="number"
                                                                step="0.01"
                                                                value={bulk.customer_margin}
                                                                onChange={(e) => updateBulkPrice(index, 'customer_margin', parseFloat(e.target.value) || 0)}
                                                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 text-black"
                                                                min="0"
                                                            />
                                                        </div>

                                                        {/* Partner Margin */}
                                                        <div>
                                                            <label className="block text-sm font-medium text-black mb-1">
                                                                Partner Margin (%)
                                                            </label>
                                                            <input
                                                                type="number"
                                                                step="0.01"
                                                                value={bulk.partner_margin}
                                                                onChange={(e) => updateBulkPrice(index, 'partner_margin', parseFloat(e.target.value) || 0)}
                                                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 text-black"
                                                                min="0"
                                                            />
                                                        </div>

                                                        {/* Delivery Fee */}
                                                        <div>
                                                            <label className="block text-sm font-medium text-black mb-1">
                                                                Delivery Fee ($)
                                                            </label>
                                                            <input
                                                                type="number"
                                                                step="0.01"
                                                                value={bulk.delivery_fee}
                                                                onChange={(e) => updateBulkPrice(index, 'delivery_fee', parseFloat(e.target.value) || 0)}
                                                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 text-black"
                                                                min="0"
                                                            />
                                                        </div>
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    )}
                                </div>

                                {/* Submit Button */}
                                <div className="flex justify-end pt-6 border-t">
                                    <button
                                        type="submit"
                                        disabled={saving}
                                        className="px-6 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
                                    >
                                        {saving ? (
                                            <div className="flex items-center">
                                                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                                                Updating...
                                            </div>
                                        ) : (
                                            'Update Pricing Information'
                                        )}
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
}
