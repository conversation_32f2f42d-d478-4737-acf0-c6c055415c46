const {
    BASE_URL,
    NEXT_PUBLIC_BASE_URL,
    WEB_SERVER_URL,
    NEXT_PUBLIC_WEB_SERVER_URL
} = process.env;

const config = {
    API_CONFIG: {
        BASE_URL: BASE_URL || 'http://localhost:3000' || 'http://localhost:3001',
        NEXT_PUBLIC_BASE_URL: NEXT_PUBLIC_BASE_URL,
        WEB_SERVER_URL: WEB_SERVER_URL,
        NEXT_PUBLIC_WEB_SERVER_URL: NEXT_PUBLIC_WEB_SERVER_URL,
    },
    USER_TYPE: {
        admin: 'admin',
        partner: 'vendor',
        seo: 'supervisor',
        superAdmin: 'superAdmin',
        user: 'user',
    },
};

export default config;